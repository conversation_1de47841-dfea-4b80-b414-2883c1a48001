{"app:open-settings": [{"modifiers": ["Alt", "Mod"], "key": "S"}], "outline:open-for-current": [], "outline:open": [], "insert-template": [{"modifiers": ["Alt"], "key": "Insert"}], "open-with-default-app:show": [{"modifiers": ["Alt", "Shift"], "key": "R"}], "obsidian-annotator:toggle-annotation-mode": [{"modifiers": ["Alt", "Shift"], "key": "4"}], "file-explorer:reveal-active-file": [{"modifiers": ["Alt"], "key": "F1"}], "editor:open-search-replace": [{"modifiers": ["Mod"], "key": "R"}], "recent-files-obsidian:recent-files-open": [{"modifiers": ["Mod"], "key": "E"}], "markdown:toggle-preview": [{"modifiers": ["Alt", "Shift"], "key": "1"}], "file-explorer:open": [{"modifiers": ["Alt"], "key": "1"}], "obsidian-git:open-git-view": [{"modifiers": ["Alt"], "key": "0"}], "editor:toggle-bold": [], "editor:rename-heading": [{"modifiers": ["Shift"], "key": "F6"}], "app:toggle-right-sidebar": [{"modifiers": ["Mod", "Shift"], "key": "F12"}], "outgoing-links:open": [{"modifiers": ["Mod"], "key": "B"}], "backlink:open": [{"modifiers": ["Alt"], "key": "F7"}], "editor:unfold-all": [{"modifiers": ["Mod", "Shift"], "key": "+"}], "editor:fold-all": [{"modifiers": ["Mod", "Shift"], "key": "-"}], "editor:fold-less": [], "editor:fold-more": [{"modifiers": ["Mod"], "key": "-"}], "file-explorer:move-file": [], "editor:toggle-fold": [{"modifiers": ["Mod"], "key": "+"}], "obsidian-quiet-outline:quiet-outline": [{"modifiers": ["Alt"], "key": "7"}], "editor:insert-embed": [], "starred:open": [{"modifiers": ["Alt"], "key": "2"}], "app:toggle-left-sidebar": [{"modifiers": ["Mod", "Shift"], "key": "F11"}], "calendar:show-calendar-view": [{"modifiers": ["Alt"], "key": "8"}], "editor:toggle-source": [{"modifiers": ["Alt", "Shift"], "key": "2"}], "editor:delete-paragraph": [], "obsidian-excalidraw-plugin:toggle-excalidraw-view": [{"modifiers": ["Alt", "Shift"], "key": "3"}], "command-palette:open": [{"modifiers": ["Mod", "Shift"], "key": "P"}], "cmdr:open-commander-settings": [], "obsidian-footnotes:insert-footnote": [{"modifiers": ["Mod", "Shift"], "key": "6"}], "editor:swap-line-down": [{"modifiers": ["Alt", "Shift"], "key": "ArrowDown"}], "editor:swap-line-up": [{"modifiers": ["Alt", "Shift"], "key": "ArrowUp"}], "smart-random-note:open-random-note": [{"modifiers": ["Alt", "Mod"], "key": "R"}, {"modifiers": ["Mod", "Shift"], "key": "R"}], "file-explorer:new-file": [], "obsidian-excalidraw-plugin:universal-add-file": [{"modifiers": ["Alt", "Shift"], "key": "Insert"}]}