---

excalidraw-plugin: parsed
tags: [excalidraw]

---
==⚠  Switch to EXCALIDRAW VIEW in the MORE OPTIONS menu of this document. ⚠== You can decompress Drawing data with the command palette: 'Decompress current Excalidraw file'. For more info check in plugin settings under 'Saving'


# Excalidraw Data

## Text Elements
aaa ^XVLA7a1X

bbb ^alQwoqlC

%%
## Drawing
```compressed-json
N4KAkARALgngDgUwgLgAQQQDwMYEMA2AlgCYBOuA7hADTgQBuCpAzoQPYB2KqATLZMzYBXUtiRoIACyhQ4zZAHoFAc0JRJQgEYA6bGwC2CgF7N6hbEcK4OCtptbErHALRY8RMpWdx8Q1TdIEfARcZgRmBShcZQUebQBGOJ4aOiCEfQQOKGZuAG1wMFAwYogSbggOSQBJAFEAKWYAISEAaTYAdTr6AE0AQSMAEQBxbGJk/hLYRHLA7CiOZWCU4shM

bgBmAHYATm0d7e2ADk2dnm2ANmOAVgnIGG54w/XtdcSbgsgKEnUHgBZD7SHAAMIP+hx4hzetykCEIymkDyBV205xBIPO8SB6yu8Su5z4Hwg1kW4lQQOhzCgpDYAGsEABhNj4NikcoAYniCE5nOWJU0uGwNOU1KEHGIjOZrIkVOszDguECWV5kAAZoR8PgAMqwJYSQQeZUQSnUuntb6SbgElZGqm0hDamC69D6srQkXwjjhHJoeLQtjy7Bqe4+kHQ

4XCOBVYje1C5AC60JV5AyUe4HCEGuhhDFWHKuCBhpFYs9zBjRWtU1J6w+AF8KQgEMRLeseL9sdtMdDGCx2Fw0Otzl2mKxOAA5ThibjnK4DnjrX7bX5Z5gDNJQRvcFUEMLQzTCMU1YIZLIx/IrQofSbwUnQLBQZUlMoSAAaADUADK9Ta4eLPiCXutL3La0n3QGB1mUIYAH0AFUKDxAAxekgROAArKCOAGYgAHkAAUH0gSs81IakqAAj4E0JIQ4GIX

B1ybH1NgXQ5tjxLZtk2clCSIDgaTTDN8GhZlBQ3NAt3wMICkA4pgNKBj0DfT9v1/Q0iOlO9DTWNA23ObRth4EEIUuac51+JdCWDXg5xRN4sSReJNiuTZDmhL5iB+ftfm0K4rgOc5ON+TFcXxd5rUkWF4XvNAeFCkpiSdLjrWNO0JRZdluS5JBdwFIUi3FJk0ulcgODlBVMnvRN1S1HUbxdJsKVtU1zUtBqTXtGryjqwthA9L0Hj9AMg0RRKSnDai

o1PSjrSTXAU3k9NM0JbNiFzCQf260ViBLGMFsEwkwlE1BcSBc5tnWVieEHQluxHPsjt9a7h17ccOEnNAWKBQ5/m2IErUfFc10O8Sd0JPdNsPdJyp2gToWo2j6IeJijlY8553idYRsgHi+LQXahLYET5OBhBoXXTAovQXAqcLSgABUNLW6nE04KBNUIIxSQM5msgQ2b1Us2LCLvXoiGUO6IDELImENbsoHMAgRbhcX9BIYglmhPQslwbMmFTCRKlq

BpmjaToen6YZRnGQkWThbMCHp8m8yZwlcCEKA2AAJXCdnSSpIQSe4nWAAkIoRH1tBiqSJlk0CIF+ekakDDhcF+OBXyuXpaaGTZsPoGpDgQzZVOvcogiIOQssJLTUH8ryDJBTYW1+HhHkc6FLMeXScRixIDM414Ytc5qfXieIEgOVjR82VvBZhOEw6Oz7oXi0lMZtNrUqldAOUynlssFMaxU38oZRK+VFQqwk1Q1B0nSNJlXX2xqEDNdyLWi1q7Vv

2qH/qwl3UkNtfq1tBqwGGmGEUkZox5CmiUGac1+KLRAjmauRIrbWjykAtAwFCIl2irWesh0W4xTRp9P6kAbq9m4Fccy1pKFjgnKSdGxDNioiuC5JaANggIzEtuAO1owYHiPFDPIQFLyQFjuBSCsF4LnCQihTY6FMI4XwrcK80w1okTYGRc80kVg4NwRo9AZNL7ngkfJIk+AACKFA2AAEd8D0n/LoiisMaJ0UOg5ZiKNfhIh4JsIS2YcaoDxtxAmd

IiZ8KjgUGOFiCA2PsY44uRjbyO2hNXaezx1jzkXP4n6gUeDkIgJZQpmxAQQiYqiQprEThDzfhsZ4ZwDibBIYFHYp1oThXnhTQeLsFgJU/nSY+Egd4ZUNPyA+eVhnGOKqVC+hpr7VUdD/A0gyX7D14Gs7+nVf4bV6qWYB1p/QCiGiGNeY0oGTUTMmBAesQkwyWigvMPBXwbWLH1XGDykoNnkgZbEDlLrxFoSUehd1sRDh7Aw16TD/JAl+E5JEa9CB

cIQDw1AxNdz7mIBDY82REF7WtHDDx8kvHIzxG2JyHDrTY3xfjQmm4+GkwZugTQrKaYUAdhTCArLNALJZmzDmlo14qhZrzFW+ABZMvJorMW5RJbrlZBCuW7gZXK1VurQkmsog61IHcuOCck4pzThnLOOc84FyLn6Ugttk74E5eUHlhpXbuy9qwQVaA/b8JKDxBAIdukPAjlcaJMkloWKkdBOCiFkJoQwlhPCySbwKlIppahLZtD/HiBcLEjd9KPHb

s2AEiRgQGUODiVivk6keSOr47y4IWwdnhYcSE5wrphVDhTZE+I/knUhB2VE8RW1xX6avNZ0yICjMyuMnKh98qShPrM8+5UFlVW2XqXZazX5VqKclOkq7nTrv/j1QBHyjoDROWAs5ECIwTRgdc2atz5pfMfE8ta6w3lbRPTg6AeDeAEP2j87gHZnJ9qYhC263AzJgeeowxGjkjg0KBcuVc3CgaMtBlinFIjPlIJKEStFpKWLkoHNkwJvFaVhPpbwi

SXrIBwDYNmPFaAzwrGYysTGxQgSXlgcUVjxQgVAlrXOM4QL2HNsHSsZwnbCl2UuJmgdQIB1cdcdxUIUBGT6BVjIRsuF6NKmwwSkolIFRQEaMtbMyhuBfrSLivVBt6hNFaB0LofRBgjDGA+CAIrsBCBjAJoEP0AtAvnDORILTxOQGULgOAEG9gT2OKcC41w/3WkyMQUzYpzOWcvBgYRWQ9Xx0TnbVO6dM7Z1zvnQuHmvM+e4AJx4OJS31aa19M6ai

ICRei+HRuiLwRfRblcFu5xfjJcM1EUgUBehaIoOFXAj6cOQFS5N0iM24lTcNEEPcFBUPUakuAKaRI4BwG1B4rLkxwoZHKKLBEEwGCEAQBQRo06pkFS3uOlU72Pu8gliIC+VR1z6G1BvF76Vd6VxKN5ki5U/vpEe5MrFY7T5zKXTdiHv3/sIRXR1NdqyCjfch3l/7gO7Sbvfps3HqOoeE+fnu++OPwc/cp+kD2R6sGnvJwzgn6RsKgI7qGdn+OoDQ

/0AhUVfMJXUJRxzwX6P+U+yFZLgXQvOWqsuwgFUpjIAU85wDsbE2psrfI/TxX/2ahiiW9og3mjk0K7R+kc3HKf0QDyl95g2BqQaj/GgEaRo3dMnwN0WrN2jBsAMKdkFBB/YPGDZrqXQvmebVZ07rFX3hQkAFZzb3qfiDagQJ11AsV2vWuIAAWTYCtU3uBNDBEidRm7WfpmyUaEyCxpBlD8gABQtwCbwBy1Ae/d4E1cAAlIaL2ygMwKhmG33AneMZ

97nOSXgc+yTeRH9HvHF8id0m53LTg0McOeZuV7HM1qFhh4W8nKvh1PUa3LtwG/hJk4XY9aQf2fo3Y+vv6/hA6+7CoQQHMMwJqMnHAKXuXpftXgyrXrjgKHLIwLTCHvgOft+ikmEMEHMFQhrD5u7PoLTD+qEtSuEttiDNNNSADmkBgZwFASQd6qpr0BgfAYgfirtmANJJ5uqOEJZjWCADWEAA
```
%%