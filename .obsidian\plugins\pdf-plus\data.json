{"displayTextFormats": [{"name": "Title & page", "template": "{{file.basename}}, p.{{pageLabel}}"}, {"name": "Page", "template": "p.{{<PERSON><PERSON><PERSON><PERSON>}}"}, {"name": "Text", "template": "{{text}}"}, {"name": "<PERSON><PERSON><PERSON>", "template": "📖"}, {"name": "None", "template": ""}], "defaultDisplayTextFormatIndex": 0, "syncDisplayTextFormat": true, "syncDefaultDisplayTextFormat": false, "copyCommands": [{"name": "Quote", "template": "> ({{linkWithDisplay}})\n> {{selection}}\n"}, {"name": "Link", "template": "{{linkWithDisplay}}"}, {"name": "Embed", "template": "!{{link}}"}, {"name": "Callout", "template": "> [!{{calloutType}}|{{color}}] {{linkWithDisplay}}\n> {{text}}\n"}, {"name": "Quote in callout", "template": "> [!{{calloutType}}|{{color}}] {{linkWithDisplay}}\n> > {{text}}\n> \n> "}], "useAnotherCopyTemplateWhenNoSelection": false, "copyTemplateWhenNoSelection": "{{linkToPageWithDisplay}}", "trimSelectionEmbed": false, "embedMargin": 50, "noSidebarInEmbed": true, "noSpreadModeInEmbed": true, "embedUnscrollable": false, "singleTabForSinglePDF": true, "highlightExistingTab": false, "existingTabHighlightOpacity": 0.5, "existingTabHighlightDuration": 0.75, "paneTypeForFirstPDFLeaf": "left", "openLinkNextToExistingPDFTab": true, "openPDFWithDefaultApp": false, "openPDFWithDefaultAppAndObsidian": true, "focusObsidianAfterOpenPDFWithDefaultApp": true, "syncWithDefaultApp": false, "dontActivateAfterOpenPDF": true, "dontActivateAfterOpenMD": true, "highlightDuration": 0.75, "noTextHighlightsInEmbed": false, "noAnnotationHighlightsInEmbed": true, "persistentTextHighlightsInEmbed": true, "persistentAnnotationHighlightsInEmbed": false, "highlightBacklinks": true, "selectionBacklinkVisualizeStyle": "highlight", "dblclickEmbedToOpenLink": true, "highlightBacklinksPane": true, "highlightOnHoverBacklinkPane": true, "backlinkHoverColor": "", "colors": {"Yellow": "#ffd000", "Red": "#ea5252", "Note": "#086ddd", "Important": "#bb61e5"}, "defaultColor": "", "defaultColorPaletteItemIndex": 0, "syncColorPaletteItem": true, "syncDefaultColorPaletteItem": false, "colorPaletteInToolbar": true, "noColorButtonInColorPalette": true, "colorPaletteInEmbedToolbar": false, "quietColorPaletteTooltip": false, "showStatusInToolbar": true, "highlightColorSpecifiedOnly": false, "doubleClickHighlightToOpenBacklink": true, "hoverHighlightAction": "preview", "paneTypeForFirstMDLeaf": "right", "singleMDLeafInSidebar": true, "alwaysUseSidebar": true, "ignoreExistingMarkdownTabIn": [], "defaultColorPaletteActionIndex": 4, "syncColorPaletteAction": true, "syncDefaultColorPaletteAction": false, "proxyMDProperty": "PDF", "hoverPDFLinkToOpen": false, "ignoreHeightParamInPopoverPreview": true, "filterBacklinksByPageDefault": true, "showBacklinkToPage": true, "enableHoverPDFInternalLink": true, "recordPDFInternalLinkHistory": true, "alwaysRecordHistory": true, "renderMarkdownInStickyNote": false, "enablePDFEdit": false, "author": "", "writeHighlightToFileOpacity": 0.2, "defaultWriteFileToggle": false, "syncWriteFileToggle": true, "syncDefaultWriteFileToggle": false, "enableAnnotationDeletion": true, "warnEveryAnnotationDelete": false, "warnBacklinkedAnnotationDelete": true, "enableAnnotationContentEdit": true, "enableEditEncryptedPDF": false, "pdfLinkColor": "#04a802", "pdfLinkBorder": false, "replaceContextMenu": true, "showContextMenuOnMouseUpIf": "Mod", "contextMenuConfig": [{"id": "action", "visible": true}, {"id": "selection", "visible": true}, {"id": "write-file", "visible": true}, {"id": "annotation", "visible": true}, {"id": "modify-annotation", "visible": true}, {"id": "link", "visible": true}, {"id": "text", "visible": true}, {"id": "search", "visible": true}, {"id": "speech", "visible": true}, {"id": "page", "visible": true}, {"id": "settings", "visible": true}], "selectionProductMenuConfig": ["color", "copy-format", "display"], "writeFileProductMenuConfig": ["color", "copy-format", "display"], "annotationProductMenuConfig": ["copy-format", "display"], "updateColorPaletteStateFromContextMenu": true, "mobileCopyAction": "pdf-plus", "showContextMenuOnTablet": false, "executeBuiltinCommandForOutline": true, "executeBuiltinCommandForZoom": true, "executeFontSizeAdjusterCommand": true, "closeSidebarWithShowCommandIfExist": true, "autoHidePDFSidebar": false, "defaultSidebarView": 1, "outlineDrag": true, "outlineContextMenu": true, "outlineLinkDisplayTextFormat": "{{file.basename}}, {{text}}", "outlineLinkCopyFormat": "{{linkWithDisplay}}", "recordHistoryOnOutlineClick": true, "popoverPreviewOnOutlineHover": true, "thumbnailDrag": true, "thumbnailContextMenu": true, "thumbnailLinkDisplayTextFormat": "{{file.basename}}, p.{{pageLabel}}", "thumbnailLinkCopyFormat": "{{linkWithDisplay}}", "recordHistoryOnThumbnailClick": true, "popoverPreviewOnThumbnailHover": true, "annotationPopupDrag": true, "showAnnotationPopupOnHover": true, "useCallout": true, "calloutType": "PDF", "calloutIcon": "highlighter", "highlightBacklinksInEmbed": false, "highlightBacklinksInHoverPopover": false, "highlightBacklinksInCanvas": true, "clickPDFInternalLinkWithModifierKey": true, "clickOutlineItemWithModifierKey": true, "clickThumbnailWithModifierKey": true, "focusEditorAfterAutoPaste": true, "clearSelectionAfterAutoPaste": true, "respectCursorPositionWhenAutoPaste": true, "blankLineAboveAppendedContent": true, "autoCopy": true, "autoFocus": false, "autoPaste": false, "autoFocusTarget": "last-active-and-open-then-last-paste", "autoPasteTarget": "last-active-and-open-then-last-paste", "openAutoFocusTargetIfNotOpened": true, "howToOpenAutoFocusTargetIfNotOpened": "right", "closeHoverEditorWhenLostFocus": true, "closeSidebarWhenLostFocus": false, "openAutoFocusTargetInEditingView": true, "executeCommandWhenTargetNotIdentified": true, "commandToExecuteWhenTargetNotIdentified": "switcher:open", "autoPasteTargetDialogTimeoutSec": 20, "autoCopyToggleRibbonIcon": true, "autoCopyIconName": "highlighter", "autoFocusToggleRibbonIcon": true, "autoFocusIconName": "zap", "autoPasteToggleRibbonIcon": true, "autoPasteIconName": "clipboard-paste", "viewSyncFollowPageNumber": true, "viewSyncPageDebounceInterval": 0.3, "openAfterExtractPages": true, "howToOpenExtractedPDF": "tab", "warnEveryPageDelete": false, "warnBacklinkedPageDelete": true, "extractPageInPlace": false, "askExtractPageInPlace": true, "pageLabelUpdateWhenInsertPage": "keep", "pageLabelUpdateWhenDeletePage": "keep", "pageLabelUpdateWhenExtractPage": "keep", "askPageLabelUpdateWhenInsertPage": true, "askPageLabelUpdateWhenDeletePage": true, "askPageLabelUpdateWhenExtractPage": true, "copyOutlineAsListFormat": "{{linkWithDisplay}}", "copyOutlineAsListDisplayTextFormat": "{{text}}", "copyOutlineAsHeadingsFormat": "{{text}}\n\n{{linkWithDisplay}}", "copyOutlineAsHeadingsDisplayTextFormat": "p.{{<PERSON><PERSON><PERSON><PERSON>}}", "copyOutlineAsHeadingsMinLevel": 2, "newFileNameFormat": "", "newFileTemplatePath": "", "newPDFLocation": "current", "newPDFFolderPath": "", "rectEmbedStaticImage": false, "rectImageFormat": "file", "rectImageExtension": "webp", "zoomToFitRect": false, "rectFollowAdaptToTheme": true, "rectEmbedResolution": 100, "includeColorWhenCopyingRectLink": true, "backlinkIconSize": 50, "showBacklinkIconForSelection": false, "showBacklinkIconForAnnotation": false, "showBacklinkIconForOffset": true, "showBacklinkIconForRect": false, "showBoundingRectForBacklinkedAnnot": false, "hideReplyAnnotation": false, "hideStampAnnotation": false, "searchLinkHighlightAll": "true", "searchLinkCaseSensitive": "true", "searchLinkMatchDiacritics": "default", "searchLinkEntireWord": "false", "dontFitWidthWhenOpenPDFLink": true, "preserveCurrentLeftOffsetWhenOpenPDFLink": false, "defaultZoomValue": "page-width", "scrollModeOnLoad": 0, "spreadModeOnLoad": 0, "usePageUpAndPageDown": true, "hoverableDropdownMenuInToolbar": true, "zoomLevelInputBoxInToolbar": true, "popoverPreviewOnExternalLinkHover": true, "actionOnCitationHover": "pdf-plus-bib-popover", "enableBibInEmbed": false, "enableBibInHoverPopover": false, "enableBibInCanvas": true, "citationIdPatterns": "^cite.\n^bib\\d+$", "copyAsSingleLine": true, "removeWhitespaceBetweenCJChars": true, "dummyFileFolderPath": "", "externalURIPatterns": [".*\\.pdf$", "https://arxiv.org/pdf/.*"], "modifierToDropExternalPDFToCreateDummy": ["Shift"], "vim": false, "vimrcPath": "", "vimVisualMotion": true, "vimScrollSize": 40, "vimLargerScrollSizeWhenZoomIn": true, "vimContinuousScrollSpeed": 1.2, "vimSmoothScroll": true, "vimHlsearch": true, "vimIncsearch": true, "enableVimInContextMenu": true, "enableVimOutlineMode": true, "vimSmoothOutlineMode": true, "vimHintChars": "hjklasdfgyuiopqwertnmzxcvb", "vimHintArgs": "all", "PATH": "", "autoCheckForUpdates": true, "fixObsidianTextSelectionBug": true}