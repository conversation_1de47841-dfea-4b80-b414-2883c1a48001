# 能量代谢类比对比：局部变量、全局变量与数据库

## 功能定位对比
| 能量形式     | 程序员类比 | 核心特征 |                             |
| -------- | ----- | ---- | --------------------------- |
| [[血糖]]  | 全局变量 | 全身即时调用（类似全局变量，所有模块可快速访问）    |
| [[肌糖原]] | 局部变量 | 肌肉专属存储（类似函数内局部变量，作用域仅限当前肌肉） |
| [[脂肪原]] | 数据库  | 长期海量存储（类似数据库，需分解操作才能读取）     |

## 供能机制对比
- **[[血糖]]（全局变量）**：快速响应爆发需求（如30秒冲刺），类似全局变量优先被调用但容量有限。
- **[[肌糖原]]（局部变量）**：仅支持短时间高强度供能（如4组深蹲），类似函数内临时变量随函数执行结束释放。
- **[[脂肪原]]（数据库）**：低强度持续供能（>30分钟慢跑），类似数据库在缓存失效后启动读取。

## 与减脂关联逻辑
当[[血糖]]（全局变量）消耗→触发[[肌糖原]]（局部变量）耗尽→肌糖原耗尽后→强制调用[[脂肪原]]（数据库）分解，形成`全局变量→局部变量→数据库`的逐级触发链路，这是减脂优先策略的核心能量调度逻辑。