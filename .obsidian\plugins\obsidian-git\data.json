{"commitMessage": "vault backup: {{date}}", "commitDateFormat": "YYYY-MM-DD HH:mm:ss", "autoSaveInterval": 0, "autoPushInterval": 0, "autoPullInterval": 0, "autoPullOnBoot": false, "disablePush": false, "pullBeforePush": true, "disablePopups": false, "listChangedFilesInMessageBody": false, "showStatusBar": true, "updateSubmodules": false, "syncMethod": "merge", "customMessageOnAutoBackup": false, "autoBackupAfterFileChange": false, "treeStructure": true, "refreshSourceControl": true, "basePath": "", "differentIntervalCommitAndPush": false, "changedFilesInStatusBar": false, "showedMobileNotice": true, "refreshSourceControlTimer": 7000, "showBranchStatusBar": true, "setLastSaveToLastCommit": false, "submoduleRecurseCheckout": false, "gitDir": "", "showFileMenu": true, "autoCommitMessage": "vault backup: {{date}}"}