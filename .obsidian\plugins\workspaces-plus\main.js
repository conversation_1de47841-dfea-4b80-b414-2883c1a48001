/*
THIS IS A GENERATED/BUNDLED FILE BY ROLLUP
if you want to view the source visit the plugins github repository
*/

'use strict';

var obsidian = require('obsidian');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var obsidian__default = /*#__PURE__*/_interopDefaultLegacy(obsidian);

/******************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */

function __awaiter(thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
}

var top = 'top';
var bottom = 'bottom';
var right = 'right';
var left = 'left';
var auto = 'auto';
var basePlacements = [top, bottom, right, left];
var start = 'start';
var end = 'end';
var clippingParents = 'clippingParents';
var viewport = 'viewport';
var popper = 'popper';
var reference = 'reference';
var variationPlacements = /*#__PURE__*/basePlacements.reduce(function (acc, placement) {
  return acc.concat([placement + "-" + start, placement + "-" + end]);
}, []);
var placements = /*#__PURE__*/[].concat(basePlacements, [auto]).reduce(function (acc, placement) {
  return acc.concat([placement, placement + "-" + start, placement + "-" + end]);
}, []); // modifiers that need to read the DOM

var beforeRead = 'beforeRead';
var read = 'read';
var afterRead = 'afterRead'; // pure-logic modifiers

var beforeMain = 'beforeMain';
var main = 'main';
var afterMain = 'afterMain'; // modifier with the purpose to write to the DOM (or write into a framework state)

var beforeWrite = 'beforeWrite';
var write = 'write';
var afterWrite = 'afterWrite';
var modifierPhases = [beforeRead, read, afterRead, beforeMain, main, afterMain, beforeWrite, write, afterWrite];

function getNodeName(element) {
  return element ? (element.nodeName || '').toLowerCase() : null;
}

function getWindow(node) {
  if (node == null) {
    return window;
  }

  if (node.toString() !== '[object Window]') {
    var ownerDocument = node.ownerDocument;
    return ownerDocument ? ownerDocument.defaultView || window : window;
  }

  return node;
}

function isElement(node) {
  var OwnElement = getWindow(node).Element;
  return node instanceof OwnElement || node instanceof Element;
}

function isHTMLElement(node) {
  var OwnElement = getWindow(node).HTMLElement;
  return node instanceof OwnElement || node instanceof HTMLElement;
}

function isShadowRoot(node) {
  // IE 11 has no ShadowRoot
  if (typeof ShadowRoot === 'undefined') {
    return false;
  }

  var OwnElement = getWindow(node).ShadowRoot;
  return node instanceof OwnElement || node instanceof ShadowRoot;
}

// and applies them to the HTMLElements such as popper and arrow

function applyStyles(_ref) {
  var state = _ref.state;
  Object.keys(state.elements).forEach(function (name) {
    var style = state.styles[name] || {};
    var attributes = state.attributes[name] || {};
    var element = state.elements[name]; // arrow is optional + virtual elements

    if (!isHTMLElement(element) || !getNodeName(element)) {
      return;
    } // Flow doesn't support to extend this property, but it's the most
    // effective way to apply styles to an HTMLElement
    // $FlowFixMe[cannot-write]


    Object.assign(element.style, style);
    Object.keys(attributes).forEach(function (name) {
      var value = attributes[name];

      if (value === false) {
        element.removeAttribute(name);
      } else {
        element.setAttribute(name, value === true ? '' : value);
      }
    });
  });
}

function effect$2(_ref2) {
  var state = _ref2.state;
  var initialStyles = {
    popper: {
      position: state.options.strategy,
      left: '0',
      top: '0',
      margin: '0'
    },
    arrow: {
      position: 'absolute'
    },
    reference: {}
  };
  Object.assign(state.elements.popper.style, initialStyles.popper);
  state.styles = initialStyles;

  if (state.elements.arrow) {
    Object.assign(state.elements.arrow.style, initialStyles.arrow);
  }

  return function () {
    Object.keys(state.elements).forEach(function (name) {
      var element = state.elements[name];
      var attributes = state.attributes[name] || {};
      var styleProperties = Object.keys(state.styles.hasOwnProperty(name) ? state.styles[name] : initialStyles[name]); // Set all values to an empty string to unset them

      var style = styleProperties.reduce(function (style, property) {
        style[property] = '';
        return style;
      }, {}); // arrow is optional + virtual elements

      if (!isHTMLElement(element) || !getNodeName(element)) {
        return;
      }

      Object.assign(element.style, style);
      Object.keys(attributes).forEach(function (attribute) {
        element.removeAttribute(attribute);
      });
    });
  };
} // eslint-disable-next-line import/no-unused-modules


var applyStyles$1 = {
  name: 'applyStyles',
  enabled: true,
  phase: 'write',
  fn: applyStyles,
  effect: effect$2,
  requires: ['computeStyles']
};

function getBasePlacement(placement) {
  return placement.split('-')[0];
}

var max = Math.max;
var min = Math.min;
var round = Math.round;

function getUAString() {
  var uaData = navigator.userAgentData;

  if (uaData != null && uaData.brands) {
    return uaData.brands.map(function (item) {
      return item.brand + "/" + item.version;
    }).join(' ');
  }

  return navigator.userAgent;
}

function isLayoutViewport() {
  return !/^((?!chrome|android).)*safari/i.test(getUAString());
}

function getBoundingClientRect(element, includeScale, isFixedStrategy) {
  if (includeScale === void 0) {
    includeScale = false;
  }

  if (isFixedStrategy === void 0) {
    isFixedStrategy = false;
  }

  var clientRect = element.getBoundingClientRect();
  var scaleX = 1;
  var scaleY = 1;

  if (includeScale && isHTMLElement(element)) {
    scaleX = element.offsetWidth > 0 ? round(clientRect.width) / element.offsetWidth || 1 : 1;
    scaleY = element.offsetHeight > 0 ? round(clientRect.height) / element.offsetHeight || 1 : 1;
  }

  var _ref = isElement(element) ? getWindow(element) : window,
      visualViewport = _ref.visualViewport;

  var addVisualOffsets = !isLayoutViewport() && isFixedStrategy;
  var x = (clientRect.left + (addVisualOffsets && visualViewport ? visualViewport.offsetLeft : 0)) / scaleX;
  var y = (clientRect.top + (addVisualOffsets && visualViewport ? visualViewport.offsetTop : 0)) / scaleY;
  var width = clientRect.width / scaleX;
  var height = clientRect.height / scaleY;
  return {
    width: width,
    height: height,
    top: y,
    right: x + width,
    bottom: y + height,
    left: x,
    x: x,
    y: y
  };
}

// means it doesn't take into account transforms.

function getLayoutRect(element) {
  var clientRect = getBoundingClientRect(element); // Use the clientRect sizes if it's not been transformed.
  // Fixes https://github.com/popperjs/popper-core/issues/1223

  var width = element.offsetWidth;
  var height = element.offsetHeight;

  if (Math.abs(clientRect.width - width) <= 1) {
    width = clientRect.width;
  }

  if (Math.abs(clientRect.height - height) <= 1) {
    height = clientRect.height;
  }

  return {
    x: element.offsetLeft,
    y: element.offsetTop,
    width: width,
    height: height
  };
}

function contains(parent, child) {
  var rootNode = child.getRootNode && child.getRootNode(); // First, attempt with faster native method

  if (parent.contains(child)) {
    return true;
  } // then fallback to custom implementation with Shadow DOM support
  else if (rootNode && isShadowRoot(rootNode)) {
      var next = child;

      do {
        if (next && parent.isSameNode(next)) {
          return true;
        } // $FlowFixMe[prop-missing]: need a better way to handle this...


        next = next.parentNode || next.host;
      } while (next);
    } // Give up, the result is false


  return false;
}

function getComputedStyle(element) {
  return getWindow(element).getComputedStyle(element);
}

function isTableElement(element) {
  return ['table', 'td', 'th'].indexOf(getNodeName(element)) >= 0;
}

function getDocumentElement(element) {
  // $FlowFixMe[incompatible-return]: assume body is always available
  return ((isElement(element) ? element.ownerDocument : // $FlowFixMe[prop-missing]
  element.document) || window.document).documentElement;
}

function getParentNode(element) {
  if (getNodeName(element) === 'html') {
    return element;
  }

  return (// this is a quicker (but less type safe) way to save quite some bytes from the bundle
    // $FlowFixMe[incompatible-return]
    // $FlowFixMe[prop-missing]
    element.assignedSlot || // step into the shadow DOM of the parent of a slotted node
    element.parentNode || ( // DOM Element detected
    isShadowRoot(element) ? element.host : null) || // ShadowRoot detected
    // $FlowFixMe[incompatible-call]: HTMLElement is a Node
    getDocumentElement(element) // fallback

  );
}

function getTrueOffsetParent(element) {
  if (!isHTMLElement(element) || // https://github.com/popperjs/popper-core/issues/837
  getComputedStyle(element).position === 'fixed') {
    return null;
  }

  return element.offsetParent;
} // `.offsetParent` reports `null` for fixed elements, while absolute elements
// return the containing block


function getContainingBlock(element) {
  var isFirefox = /firefox/i.test(getUAString());
  var isIE = /Trident/i.test(getUAString());

  if (isIE && isHTMLElement(element)) {
    // In IE 9, 10 and 11 fixed elements containing block is always established by the viewport
    var elementCss = getComputedStyle(element);

    if (elementCss.position === 'fixed') {
      return null;
    }
  }

  var currentNode = getParentNode(element);

  if (isShadowRoot(currentNode)) {
    currentNode = currentNode.host;
  }

  while (isHTMLElement(currentNode) && ['html', 'body'].indexOf(getNodeName(currentNode)) < 0) {
    var css = getComputedStyle(currentNode); // This is non-exhaustive but covers the most common CSS properties that
    // create a containing block.
    // https://developer.mozilla.org/en-US/docs/Web/CSS/Containing_block#identifying_the_containing_block

    if (css.transform !== 'none' || css.perspective !== 'none' || css.contain === 'paint' || ['transform', 'perspective'].indexOf(css.willChange) !== -1 || isFirefox && css.willChange === 'filter' || isFirefox && css.filter && css.filter !== 'none') {
      return currentNode;
    } else {
      currentNode = currentNode.parentNode;
    }
  }

  return null;
} // Gets the closest ancestor positioned element. Handles some edge cases,
// such as table ancestors and cross browser bugs.


function getOffsetParent(element) {
  var window = getWindow(element);
  var offsetParent = getTrueOffsetParent(element);

  while (offsetParent && isTableElement(offsetParent) && getComputedStyle(offsetParent).position === 'static') {
    offsetParent = getTrueOffsetParent(offsetParent);
  }

  if (offsetParent && (getNodeName(offsetParent) === 'html' || getNodeName(offsetParent) === 'body' && getComputedStyle(offsetParent).position === 'static')) {
    return window;
  }

  return offsetParent || getContainingBlock(element) || window;
}

function getMainAxisFromPlacement(placement) {
  return ['top', 'bottom'].indexOf(placement) >= 0 ? 'x' : 'y';
}

function within(min$1, value, max$1) {
  return max(min$1, min(value, max$1));
}
function withinMaxClamp(min, value, max) {
  var v = within(min, value, max);
  return v > max ? max : v;
}

function getFreshSideObject() {
  return {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  };
}

function mergePaddingObject(paddingObject) {
  return Object.assign({}, getFreshSideObject(), paddingObject);
}

function expandToHashMap(value, keys) {
  return keys.reduce(function (hashMap, key) {
    hashMap[key] = value;
    return hashMap;
  }, {});
}

var toPaddingObject = function toPaddingObject(padding, state) {
  padding = typeof padding === 'function' ? padding(Object.assign({}, state.rects, {
    placement: state.placement
  })) : padding;
  return mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));
};

function arrow(_ref) {
  var _state$modifiersData$;

  var state = _ref.state,
      name = _ref.name,
      options = _ref.options;
  var arrowElement = state.elements.arrow;
  var popperOffsets = state.modifiersData.popperOffsets;
  var basePlacement = getBasePlacement(state.placement);
  var axis = getMainAxisFromPlacement(basePlacement);
  var isVertical = [left, right].indexOf(basePlacement) >= 0;
  var len = isVertical ? 'height' : 'width';

  if (!arrowElement || !popperOffsets) {
    return;
  }

  var paddingObject = toPaddingObject(options.padding, state);
  var arrowRect = getLayoutRect(arrowElement);
  var minProp = axis === 'y' ? top : left;
  var maxProp = axis === 'y' ? bottom : right;
  var endDiff = state.rects.reference[len] + state.rects.reference[axis] - popperOffsets[axis] - state.rects.popper[len];
  var startDiff = popperOffsets[axis] - state.rects.reference[axis];
  var arrowOffsetParent = getOffsetParent(arrowElement);
  var clientSize = arrowOffsetParent ? axis === 'y' ? arrowOffsetParent.clientHeight || 0 : arrowOffsetParent.clientWidth || 0 : 0;
  var centerToReference = endDiff / 2 - startDiff / 2; // Make sure the arrow doesn't overflow the popper if the center point is
  // outside of the popper bounds

  var min = paddingObject[minProp];
  var max = clientSize - arrowRect[len] - paddingObject[maxProp];
  var center = clientSize / 2 - arrowRect[len] / 2 + centerToReference;
  var offset = within(min, center, max); // Prevents breaking syntax highlighting...

  var axisProp = axis;
  state.modifiersData[name] = (_state$modifiersData$ = {}, _state$modifiersData$[axisProp] = offset, _state$modifiersData$.centerOffset = offset - center, _state$modifiersData$);
}

function effect$1(_ref2) {
  var state = _ref2.state,
      options = _ref2.options;
  var _options$element = options.element,
      arrowElement = _options$element === void 0 ? '[data-popper-arrow]' : _options$element;

  if (arrowElement == null) {
    return;
  } // CSS selector


  if (typeof arrowElement === 'string') {
    arrowElement = state.elements.popper.querySelector(arrowElement);

    if (!arrowElement) {
      return;
    }
  }

  if (process.env.NODE_ENV !== "production") {
    if (!isHTMLElement(arrowElement)) {
      console.error(['Popper: "arrow" element must be an HTMLElement (not an SVGElement).', 'To use an SVG arrow, wrap it in an HTMLElement that will be used as', 'the arrow.'].join(' '));
    }
  }

  if (!contains(state.elements.popper, arrowElement)) {
    if (process.env.NODE_ENV !== "production") {
      console.error(['Popper: "arrow" modifier\'s `element` must be a child of the popper', 'element.'].join(' '));
    }

    return;
  }

  state.elements.arrow = arrowElement;
} // eslint-disable-next-line import/no-unused-modules


var arrow$1 = {
  name: 'arrow',
  enabled: true,
  phase: 'main',
  fn: arrow,
  effect: effect$1,
  requires: ['popperOffsets'],
  requiresIfExists: ['preventOverflow']
};

function getVariation(placement) {
  return placement.split('-')[1];
}

var unsetSides = {
  top: 'auto',
  right: 'auto',
  bottom: 'auto',
  left: 'auto'
}; // Round the offsets to the nearest suitable subpixel based on the DPR.
// Zooming can change the DPR, but it seems to report a value that will
// cleanly divide the values into the appropriate subpixels.

function roundOffsetsByDPR(_ref) {
  var x = _ref.x,
      y = _ref.y;
  var win = window;
  var dpr = win.devicePixelRatio || 1;
  return {
    x: round(x * dpr) / dpr || 0,
    y: round(y * dpr) / dpr || 0
  };
}

function mapToStyles(_ref2) {
  var _Object$assign2;

  var popper = _ref2.popper,
      popperRect = _ref2.popperRect,
      placement = _ref2.placement,
      variation = _ref2.variation,
      offsets = _ref2.offsets,
      position = _ref2.position,
      gpuAcceleration = _ref2.gpuAcceleration,
      adaptive = _ref2.adaptive,
      roundOffsets = _ref2.roundOffsets,
      isFixed = _ref2.isFixed;
  var _offsets$x = offsets.x,
      x = _offsets$x === void 0 ? 0 : _offsets$x,
      _offsets$y = offsets.y,
      y = _offsets$y === void 0 ? 0 : _offsets$y;

  var _ref3 = typeof roundOffsets === 'function' ? roundOffsets({
    x: x,
    y: y
  }) : {
    x: x,
    y: y
  };

  x = _ref3.x;
  y = _ref3.y;
  var hasX = offsets.hasOwnProperty('x');
  var hasY = offsets.hasOwnProperty('y');
  var sideX = left;
  var sideY = top;
  var win = window;

  if (adaptive) {
    var offsetParent = getOffsetParent(popper);
    var heightProp = 'clientHeight';
    var widthProp = 'clientWidth';

    if (offsetParent === getWindow(popper)) {
      offsetParent = getDocumentElement(popper);

      if (getComputedStyle(offsetParent).position !== 'static' && position === 'absolute') {
        heightProp = 'scrollHeight';
        widthProp = 'scrollWidth';
      }
    } // $FlowFixMe[incompatible-cast]: force type refinement, we compare offsetParent with window above, but Flow doesn't detect it


    offsetParent = offsetParent;

    if (placement === top || (placement === left || placement === right) && variation === end) {
      sideY = bottom;
      var offsetY = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.height : // $FlowFixMe[prop-missing]
      offsetParent[heightProp];
      y -= offsetY - popperRect.height;
      y *= gpuAcceleration ? 1 : -1;
    }

    if (placement === left || (placement === top || placement === bottom) && variation === end) {
      sideX = right;
      var offsetX = isFixed && offsetParent === win && win.visualViewport ? win.visualViewport.width : // $FlowFixMe[prop-missing]
      offsetParent[widthProp];
      x -= offsetX - popperRect.width;
      x *= gpuAcceleration ? 1 : -1;
    }
  }

  var commonStyles = Object.assign({
    position: position
  }, adaptive && unsetSides);

  var _ref4 = roundOffsets === true ? roundOffsetsByDPR({
    x: x,
    y: y
  }) : {
    x: x,
    y: y
  };

  x = _ref4.x;
  y = _ref4.y;

  if (gpuAcceleration) {
    var _Object$assign;

    return Object.assign({}, commonStyles, (_Object$assign = {}, _Object$assign[sideY] = hasY ? '0' : '', _Object$assign[sideX] = hasX ? '0' : '', _Object$assign.transform = (win.devicePixelRatio || 1) <= 1 ? "translate(" + x + "px, " + y + "px)" : "translate3d(" + x + "px, " + y + "px, 0)", _Object$assign));
  }

  return Object.assign({}, commonStyles, (_Object$assign2 = {}, _Object$assign2[sideY] = hasY ? y + "px" : '', _Object$assign2[sideX] = hasX ? x + "px" : '', _Object$assign2.transform = '', _Object$assign2));
}

function computeStyles(_ref5) {
  var state = _ref5.state,
      options = _ref5.options;
  var _options$gpuAccelerat = options.gpuAcceleration,
      gpuAcceleration = _options$gpuAccelerat === void 0 ? true : _options$gpuAccelerat,
      _options$adaptive = options.adaptive,
      adaptive = _options$adaptive === void 0 ? true : _options$adaptive,
      _options$roundOffsets = options.roundOffsets,
      roundOffsets = _options$roundOffsets === void 0 ? true : _options$roundOffsets;

  if (process.env.NODE_ENV !== "production") {
    var transitionProperty = getComputedStyle(state.elements.popper).transitionProperty || '';

    if (adaptive && ['transform', 'top', 'right', 'bottom', 'left'].some(function (property) {
      return transitionProperty.indexOf(property) >= 0;
    })) {
      console.warn(['Popper: Detected CSS transitions on at least one of the following', 'CSS properties: "transform", "top", "right", "bottom", "left".', '\n\n', 'Disable the "computeStyles" modifier\'s `adaptive` option to allow', 'for smooth transitions, or remove these properties from the CSS', 'transition declaration on the popper element if only transitioning', 'opacity or background-color for example.', '\n\n', 'We recommend using the popper element as a wrapper around an inner', 'element that can have any CSS property transitioned for animations.'].join(' '));
    }
  }

  var commonStyles = {
    placement: getBasePlacement(state.placement),
    variation: getVariation(state.placement),
    popper: state.elements.popper,
    popperRect: state.rects.popper,
    gpuAcceleration: gpuAcceleration,
    isFixed: state.options.strategy === 'fixed'
  };

  if (state.modifiersData.popperOffsets != null) {
    state.styles.popper = Object.assign({}, state.styles.popper, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.popperOffsets,
      position: state.options.strategy,
      adaptive: adaptive,
      roundOffsets: roundOffsets
    })));
  }

  if (state.modifiersData.arrow != null) {
    state.styles.arrow = Object.assign({}, state.styles.arrow, mapToStyles(Object.assign({}, commonStyles, {
      offsets: state.modifiersData.arrow,
      position: 'absolute',
      adaptive: false,
      roundOffsets: roundOffsets
    })));
  }

  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    'data-popper-placement': state.placement
  });
} // eslint-disable-next-line import/no-unused-modules


var computeStyles$1 = {
  name: 'computeStyles',
  enabled: true,
  phase: 'beforeWrite',
  fn: computeStyles,
  data: {}
};

var passive = {
  passive: true
};

function effect(_ref) {
  var state = _ref.state,
      instance = _ref.instance,
      options = _ref.options;
  var _options$scroll = options.scroll,
      scroll = _options$scroll === void 0 ? true : _options$scroll,
      _options$resize = options.resize,
      resize = _options$resize === void 0 ? true : _options$resize;
  var window = getWindow(state.elements.popper);
  var scrollParents = [].concat(state.scrollParents.reference, state.scrollParents.popper);

  if (scroll) {
    scrollParents.forEach(function (scrollParent) {
      scrollParent.addEventListener('scroll', instance.update, passive);
    });
  }

  if (resize) {
    window.addEventListener('resize', instance.update, passive);
  }

  return function () {
    if (scroll) {
      scrollParents.forEach(function (scrollParent) {
        scrollParent.removeEventListener('scroll', instance.update, passive);
      });
    }

    if (resize) {
      window.removeEventListener('resize', instance.update, passive);
    }
  };
} // eslint-disable-next-line import/no-unused-modules


var eventListeners = {
  name: 'eventListeners',
  enabled: true,
  phase: 'write',
  fn: function fn() {},
  effect: effect,
  data: {}
};

var hash$1 = {
  left: 'right',
  right: 'left',
  bottom: 'top',
  top: 'bottom'
};
function getOppositePlacement(placement) {
  return placement.replace(/left|right|bottom|top/g, function (matched) {
    return hash$1[matched];
  });
}

var hash = {
  start: 'end',
  end: 'start'
};
function getOppositeVariationPlacement(placement) {
  return placement.replace(/start|end/g, function (matched) {
    return hash[matched];
  });
}

function getWindowScroll(node) {
  var win = getWindow(node);
  var scrollLeft = win.pageXOffset;
  var scrollTop = win.pageYOffset;
  return {
    scrollLeft: scrollLeft,
    scrollTop: scrollTop
  };
}

function getWindowScrollBarX(element) {
  // If <html> has a CSS width greater than the viewport, then this will be
  // incorrect for RTL.
  // Popper 1 is broken in this case and never had a bug report so let's assume
  // it's not an issue. I don't think anyone ever specifies width on <html>
  // anyway.
  // Browsers where the left scrollbar doesn't cause an issue report `0` for
  // this (e.g. Edge 2019, IE11, Safari)
  return getBoundingClientRect(getDocumentElement(element)).left + getWindowScroll(element).scrollLeft;
}

function getViewportRect(element, strategy) {
  var win = getWindow(element);
  var html = getDocumentElement(element);
  var visualViewport = win.visualViewport;
  var width = html.clientWidth;
  var height = html.clientHeight;
  var x = 0;
  var y = 0;

  if (visualViewport) {
    width = visualViewport.width;
    height = visualViewport.height;
    var layoutViewport = isLayoutViewport();

    if (layoutViewport || !layoutViewport && strategy === 'fixed') {
      x = visualViewport.offsetLeft;
      y = visualViewport.offsetTop;
    }
  }

  return {
    width: width,
    height: height,
    x: x + getWindowScrollBarX(element),
    y: y
  };
}

// of the `<html>` and `<body>` rect bounds if horizontally scrollable

function getDocumentRect(element) {
  var _element$ownerDocumen;

  var html = getDocumentElement(element);
  var winScroll = getWindowScroll(element);
  var body = (_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body;
  var width = max(html.scrollWidth, html.clientWidth, body ? body.scrollWidth : 0, body ? body.clientWidth : 0);
  var height = max(html.scrollHeight, html.clientHeight, body ? body.scrollHeight : 0, body ? body.clientHeight : 0);
  var x = -winScroll.scrollLeft + getWindowScrollBarX(element);
  var y = -winScroll.scrollTop;

  if (getComputedStyle(body || html).direction === 'rtl') {
    x += max(html.clientWidth, body ? body.clientWidth : 0) - width;
  }

  return {
    width: width,
    height: height,
    x: x,
    y: y
  };
}

function isScrollParent(element) {
  // Firefox wants us to check `-x` and `-y` variations as well
  var _getComputedStyle = getComputedStyle(element),
      overflow = _getComputedStyle.overflow,
      overflowX = _getComputedStyle.overflowX,
      overflowY = _getComputedStyle.overflowY;

  return /auto|scroll|overlay|hidden/.test(overflow + overflowY + overflowX);
}

function getScrollParent(node) {
  if (['html', 'body', '#document'].indexOf(getNodeName(node)) >= 0) {
    // $FlowFixMe[incompatible-return]: assume body is always available
    return node.ownerDocument.body;
  }

  if (isHTMLElement(node) && isScrollParent(node)) {
    return node;
  }

  return getScrollParent(getParentNode(node));
}

/*
given a DOM element, return the list of all scroll parents, up the list of ancesors
until we get to the top window object. This list is what we attach scroll listeners
to, because if any of these parent elements scroll, we'll need to re-calculate the
reference element's position.
*/

function listScrollParents(element, list) {
  var _element$ownerDocumen;

  if (list === void 0) {
    list = [];
  }

  var scrollParent = getScrollParent(element);
  var isBody = scrollParent === ((_element$ownerDocumen = element.ownerDocument) == null ? void 0 : _element$ownerDocumen.body);
  var win = getWindow(scrollParent);
  var target = isBody ? [win].concat(win.visualViewport || [], isScrollParent(scrollParent) ? scrollParent : []) : scrollParent;
  var updatedList = list.concat(target);
  return isBody ? updatedList : // $FlowFixMe[incompatible-call]: isBody tells us target will be an HTMLElement here
  updatedList.concat(listScrollParents(getParentNode(target)));
}

function rectToClientRect(rect) {
  return Object.assign({}, rect, {
    left: rect.x,
    top: rect.y,
    right: rect.x + rect.width,
    bottom: rect.y + rect.height
  });
}

function getInnerBoundingClientRect(element, strategy) {
  var rect = getBoundingClientRect(element, false, strategy === 'fixed');
  rect.top = rect.top + element.clientTop;
  rect.left = rect.left + element.clientLeft;
  rect.bottom = rect.top + element.clientHeight;
  rect.right = rect.left + element.clientWidth;
  rect.width = element.clientWidth;
  rect.height = element.clientHeight;
  rect.x = rect.left;
  rect.y = rect.top;
  return rect;
}

function getClientRectFromMixedType(element, clippingParent, strategy) {
  return clippingParent === viewport ? rectToClientRect(getViewportRect(element, strategy)) : isElement(clippingParent) ? getInnerBoundingClientRect(clippingParent, strategy) : rectToClientRect(getDocumentRect(getDocumentElement(element)));
} // A "clipping parent" is an overflowable container with the characteristic of
// clipping (or hiding) overflowing elements with a position different from
// `initial`


function getClippingParents(element) {
  var clippingParents = listScrollParents(getParentNode(element));
  var canEscapeClipping = ['absolute', 'fixed'].indexOf(getComputedStyle(element).position) >= 0;
  var clipperElement = canEscapeClipping && isHTMLElement(element) ? getOffsetParent(element) : element;

  if (!isElement(clipperElement)) {
    return [];
  } // $FlowFixMe[incompatible-return]: https://github.com/facebook/flow/issues/1414


  return clippingParents.filter(function (clippingParent) {
    return isElement(clippingParent) && contains(clippingParent, clipperElement) && getNodeName(clippingParent) !== 'body';
  });
} // Gets the maximum area that the element is visible in due to any number of
// clipping parents


function getClippingRect(element, boundary, rootBoundary, strategy) {
  var mainClippingParents = boundary === 'clippingParents' ? getClippingParents(element) : [].concat(boundary);
  var clippingParents = [].concat(mainClippingParents, [rootBoundary]);
  var firstClippingParent = clippingParents[0];
  var clippingRect = clippingParents.reduce(function (accRect, clippingParent) {
    var rect = getClientRectFromMixedType(element, clippingParent, strategy);
    accRect.top = max(rect.top, accRect.top);
    accRect.right = min(rect.right, accRect.right);
    accRect.bottom = min(rect.bottom, accRect.bottom);
    accRect.left = max(rect.left, accRect.left);
    return accRect;
  }, getClientRectFromMixedType(element, firstClippingParent, strategy));
  clippingRect.width = clippingRect.right - clippingRect.left;
  clippingRect.height = clippingRect.bottom - clippingRect.top;
  clippingRect.x = clippingRect.left;
  clippingRect.y = clippingRect.top;
  return clippingRect;
}

function computeOffsets(_ref) {
  var reference = _ref.reference,
      element = _ref.element,
      placement = _ref.placement;
  var basePlacement = placement ? getBasePlacement(placement) : null;
  var variation = placement ? getVariation(placement) : null;
  var commonX = reference.x + reference.width / 2 - element.width / 2;
  var commonY = reference.y + reference.height / 2 - element.height / 2;
  var offsets;

  switch (basePlacement) {
    case top:
      offsets = {
        x: commonX,
        y: reference.y - element.height
      };
      break;

    case bottom:
      offsets = {
        x: commonX,
        y: reference.y + reference.height
      };
      break;

    case right:
      offsets = {
        x: reference.x + reference.width,
        y: commonY
      };
      break;

    case left:
      offsets = {
        x: reference.x - element.width,
        y: commonY
      };
      break;

    default:
      offsets = {
        x: reference.x,
        y: reference.y
      };
  }

  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;

  if (mainAxis != null) {
    var len = mainAxis === 'y' ? 'height' : 'width';

    switch (variation) {
      case start:
        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);
        break;

      case end:
        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);
        break;
    }
  }

  return offsets;
}

function detectOverflow(state, options) {
  if (options === void 0) {
    options = {};
  }

  var _options = options,
      _options$placement = _options.placement,
      placement = _options$placement === void 0 ? state.placement : _options$placement,
      _options$strategy = _options.strategy,
      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,
      _options$boundary = _options.boundary,
      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,
      _options$rootBoundary = _options.rootBoundary,
      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,
      _options$elementConte = _options.elementContext,
      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,
      _options$altBoundary = _options.altBoundary,
      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,
      _options$padding = _options.padding,
      padding = _options$padding === void 0 ? 0 : _options$padding;
  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));
  var altContext = elementContext === popper ? reference : popper;
  var popperRect = state.rects.popper;
  var element = state.elements[altBoundary ? altContext : elementContext];
  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);
  var referenceClientRect = getBoundingClientRect(state.elements.reference);
  var popperOffsets = computeOffsets({
    reference: referenceClientRect,
    element: popperRect,
    strategy: 'absolute',
    placement: placement
  });
  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));
  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect
  // 0 or negative = within the clipping rect

  var overflowOffsets = {
    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,
    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,
    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,
    right: elementClientRect.right - clippingClientRect.right + paddingObject.right
  };
  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element

  if (elementContext === popper && offsetData) {
    var offset = offsetData[placement];
    Object.keys(overflowOffsets).forEach(function (key) {
      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;
      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';
      overflowOffsets[key] += offset[axis] * multiply;
    });
  }

  return overflowOffsets;
}

function computeAutoPlacement(state, options) {
  if (options === void 0) {
    options = {};
  }

  var _options = options,
      placement = _options.placement,
      boundary = _options.boundary,
      rootBoundary = _options.rootBoundary,
      padding = _options.padding,
      flipVariations = _options.flipVariations,
      _options$allowedAutoP = _options.allowedAutoPlacements,
      allowedAutoPlacements = _options$allowedAutoP === void 0 ? placements : _options$allowedAutoP;
  var variation = getVariation(placement);
  var placements$1 = variation ? flipVariations ? variationPlacements : variationPlacements.filter(function (placement) {
    return getVariation(placement) === variation;
  }) : basePlacements;
  var allowedPlacements = placements$1.filter(function (placement) {
    return allowedAutoPlacements.indexOf(placement) >= 0;
  });

  if (allowedPlacements.length === 0) {
    allowedPlacements = placements$1;

    if (process.env.NODE_ENV !== "production") {
      console.error(['Popper: The `allowedAutoPlacements` option did not allow any', 'placements. Ensure the `placement` option matches the variation', 'of the allowed placements.', 'For example, "auto" cannot be used to allow "bottom-start".', 'Use "auto-start" instead.'].join(' '));
    }
  } // $FlowFixMe[incompatible-type]: Flow seems to have problems with two array unions...


  var overflows = allowedPlacements.reduce(function (acc, placement) {
    acc[placement] = detectOverflow(state, {
      placement: placement,
      boundary: boundary,
      rootBoundary: rootBoundary,
      padding: padding
    })[getBasePlacement(placement)];
    return acc;
  }, {});
  return Object.keys(overflows).sort(function (a, b) {
    return overflows[a] - overflows[b];
  });
}

function getExpandedFallbackPlacements(placement) {
  if (getBasePlacement(placement) === auto) {
    return [];
  }

  var oppositePlacement = getOppositePlacement(placement);
  return [getOppositeVariationPlacement(placement), oppositePlacement, getOppositeVariationPlacement(oppositePlacement)];
}

function flip(_ref) {
  var state = _ref.state,
      options = _ref.options,
      name = _ref.name;

  if (state.modifiersData[name]._skip) {
    return;
  }

  var _options$mainAxis = options.mainAxis,
      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,
      _options$altAxis = options.altAxis,
      checkAltAxis = _options$altAxis === void 0 ? true : _options$altAxis,
      specifiedFallbackPlacements = options.fallbackPlacements,
      padding = options.padding,
      boundary = options.boundary,
      rootBoundary = options.rootBoundary,
      altBoundary = options.altBoundary,
      _options$flipVariatio = options.flipVariations,
      flipVariations = _options$flipVariatio === void 0 ? true : _options$flipVariatio,
      allowedAutoPlacements = options.allowedAutoPlacements;
  var preferredPlacement = state.options.placement;
  var basePlacement = getBasePlacement(preferredPlacement);
  var isBasePlacement = basePlacement === preferredPlacement;
  var fallbackPlacements = specifiedFallbackPlacements || (isBasePlacement || !flipVariations ? [getOppositePlacement(preferredPlacement)] : getExpandedFallbackPlacements(preferredPlacement));
  var placements = [preferredPlacement].concat(fallbackPlacements).reduce(function (acc, placement) {
    return acc.concat(getBasePlacement(placement) === auto ? computeAutoPlacement(state, {
      placement: placement,
      boundary: boundary,
      rootBoundary: rootBoundary,
      padding: padding,
      flipVariations: flipVariations,
      allowedAutoPlacements: allowedAutoPlacements
    }) : placement);
  }, []);
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var checksMap = new Map();
  var makeFallbackChecks = true;
  var firstFittingPlacement = placements[0];

  for (var i = 0; i < placements.length; i++) {
    var placement = placements[i];

    var _basePlacement = getBasePlacement(placement);

    var isStartVariation = getVariation(placement) === start;
    var isVertical = [top, bottom].indexOf(_basePlacement) >= 0;
    var len = isVertical ? 'width' : 'height';
    var overflow = detectOverflow(state, {
      placement: placement,
      boundary: boundary,
      rootBoundary: rootBoundary,
      altBoundary: altBoundary,
      padding: padding
    });
    var mainVariationSide = isVertical ? isStartVariation ? right : left : isStartVariation ? bottom : top;

    if (referenceRect[len] > popperRect[len]) {
      mainVariationSide = getOppositePlacement(mainVariationSide);
    }

    var altVariationSide = getOppositePlacement(mainVariationSide);
    var checks = [];

    if (checkMainAxis) {
      checks.push(overflow[_basePlacement] <= 0);
    }

    if (checkAltAxis) {
      checks.push(overflow[mainVariationSide] <= 0, overflow[altVariationSide] <= 0);
    }

    if (checks.every(function (check) {
      return check;
    })) {
      firstFittingPlacement = placement;
      makeFallbackChecks = false;
      break;
    }

    checksMap.set(placement, checks);
  }

  if (makeFallbackChecks) {
    // `2` may be desired in some cases – research later
    var numberOfChecks = flipVariations ? 3 : 1;

    var _loop = function _loop(_i) {
      var fittingPlacement = placements.find(function (placement) {
        var checks = checksMap.get(placement);

        if (checks) {
          return checks.slice(0, _i).every(function (check) {
            return check;
          });
        }
      });

      if (fittingPlacement) {
        firstFittingPlacement = fittingPlacement;
        return "break";
      }
    };

    for (var _i = numberOfChecks; _i > 0; _i--) {
      var _ret = _loop(_i);

      if (_ret === "break") break;
    }
  }

  if (state.placement !== firstFittingPlacement) {
    state.modifiersData[name]._skip = true;
    state.placement = firstFittingPlacement;
    state.reset = true;
  }
} // eslint-disable-next-line import/no-unused-modules


var flip$1 = {
  name: 'flip',
  enabled: true,
  phase: 'main',
  fn: flip,
  requiresIfExists: ['offset'],
  data: {
    _skip: false
  }
};

function getSideOffsets(overflow, rect, preventedOffsets) {
  if (preventedOffsets === void 0) {
    preventedOffsets = {
      x: 0,
      y: 0
    };
  }

  return {
    top: overflow.top - rect.height - preventedOffsets.y,
    right: overflow.right - rect.width + preventedOffsets.x,
    bottom: overflow.bottom - rect.height + preventedOffsets.y,
    left: overflow.left - rect.width - preventedOffsets.x
  };
}

function isAnySideFullyClipped(overflow) {
  return [top, right, bottom, left].some(function (side) {
    return overflow[side] >= 0;
  });
}

function hide(_ref) {
  var state = _ref.state,
      name = _ref.name;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var preventedOffsets = state.modifiersData.preventOverflow;
  var referenceOverflow = detectOverflow(state, {
    elementContext: 'reference'
  });
  var popperAltOverflow = detectOverflow(state, {
    altBoundary: true
  });
  var referenceClippingOffsets = getSideOffsets(referenceOverflow, referenceRect);
  var popperEscapeOffsets = getSideOffsets(popperAltOverflow, popperRect, preventedOffsets);
  var isReferenceHidden = isAnySideFullyClipped(referenceClippingOffsets);
  var hasPopperEscaped = isAnySideFullyClipped(popperEscapeOffsets);
  state.modifiersData[name] = {
    referenceClippingOffsets: referenceClippingOffsets,
    popperEscapeOffsets: popperEscapeOffsets,
    isReferenceHidden: isReferenceHidden,
    hasPopperEscaped: hasPopperEscaped
  };
  state.attributes.popper = Object.assign({}, state.attributes.popper, {
    'data-popper-reference-hidden': isReferenceHidden,
    'data-popper-escaped': hasPopperEscaped
  });
} // eslint-disable-next-line import/no-unused-modules


var hide$1 = {
  name: 'hide',
  enabled: true,
  phase: 'main',
  requiresIfExists: ['preventOverflow'],
  fn: hide
};

function distanceAndSkiddingToXY(placement, rects, offset) {
  var basePlacement = getBasePlacement(placement);
  var invertDistance = [left, top].indexOf(basePlacement) >= 0 ? -1 : 1;

  var _ref = typeof offset === 'function' ? offset(Object.assign({}, rects, {
    placement: placement
  })) : offset,
      skidding = _ref[0],
      distance = _ref[1];

  skidding = skidding || 0;
  distance = (distance || 0) * invertDistance;
  return [left, right].indexOf(basePlacement) >= 0 ? {
    x: distance,
    y: skidding
  } : {
    x: skidding,
    y: distance
  };
}

function offset(_ref2) {
  var state = _ref2.state,
      options = _ref2.options,
      name = _ref2.name;
  var _options$offset = options.offset,
      offset = _options$offset === void 0 ? [0, 0] : _options$offset;
  var data = placements.reduce(function (acc, placement) {
    acc[placement] = distanceAndSkiddingToXY(placement, state.rects, offset);
    return acc;
  }, {});
  var _data$state$placement = data[state.placement],
      x = _data$state$placement.x,
      y = _data$state$placement.y;

  if (state.modifiersData.popperOffsets != null) {
    state.modifiersData.popperOffsets.x += x;
    state.modifiersData.popperOffsets.y += y;
  }

  state.modifiersData[name] = data;
} // eslint-disable-next-line import/no-unused-modules


var offset$1 = {
  name: 'offset',
  enabled: true,
  phase: 'main',
  requires: ['popperOffsets'],
  fn: offset
};

function popperOffsets(_ref) {
  var state = _ref.state,
      name = _ref.name;
  // Offsets are the actual position the popper needs to have to be
  // properly positioned near its reference element
  // This is the most basic placement, and will be adjusted by
  // the modifiers in the next step
  state.modifiersData[name] = computeOffsets({
    reference: state.rects.reference,
    element: state.rects.popper,
    strategy: 'absolute',
    placement: state.placement
  });
} // eslint-disable-next-line import/no-unused-modules


var popperOffsets$1 = {
  name: 'popperOffsets',
  enabled: true,
  phase: 'read',
  fn: popperOffsets,
  data: {}
};

function getAltAxis(axis) {
  return axis === 'x' ? 'y' : 'x';
}

function preventOverflow(_ref) {
  var state = _ref.state,
      options = _ref.options,
      name = _ref.name;
  var _options$mainAxis = options.mainAxis,
      checkMainAxis = _options$mainAxis === void 0 ? true : _options$mainAxis,
      _options$altAxis = options.altAxis,
      checkAltAxis = _options$altAxis === void 0 ? false : _options$altAxis,
      boundary = options.boundary,
      rootBoundary = options.rootBoundary,
      altBoundary = options.altBoundary,
      padding = options.padding,
      _options$tether = options.tether,
      tether = _options$tether === void 0 ? true : _options$tether,
      _options$tetherOffset = options.tetherOffset,
      tetherOffset = _options$tetherOffset === void 0 ? 0 : _options$tetherOffset;
  var overflow = detectOverflow(state, {
    boundary: boundary,
    rootBoundary: rootBoundary,
    padding: padding,
    altBoundary: altBoundary
  });
  var basePlacement = getBasePlacement(state.placement);
  var variation = getVariation(state.placement);
  var isBasePlacement = !variation;
  var mainAxis = getMainAxisFromPlacement(basePlacement);
  var altAxis = getAltAxis(mainAxis);
  var popperOffsets = state.modifiersData.popperOffsets;
  var referenceRect = state.rects.reference;
  var popperRect = state.rects.popper;
  var tetherOffsetValue = typeof tetherOffset === 'function' ? tetherOffset(Object.assign({}, state.rects, {
    placement: state.placement
  })) : tetherOffset;
  var normalizedTetherOffsetValue = typeof tetherOffsetValue === 'number' ? {
    mainAxis: tetherOffsetValue,
    altAxis: tetherOffsetValue
  } : Object.assign({
    mainAxis: 0,
    altAxis: 0
  }, tetherOffsetValue);
  var offsetModifierState = state.modifiersData.offset ? state.modifiersData.offset[state.placement] : null;
  var data = {
    x: 0,
    y: 0
  };

  if (!popperOffsets) {
    return;
  }

  if (checkMainAxis) {
    var _offsetModifierState$;

    var mainSide = mainAxis === 'y' ? top : left;
    var altSide = mainAxis === 'y' ? bottom : right;
    var len = mainAxis === 'y' ? 'height' : 'width';
    var offset = popperOffsets[mainAxis];
    var min$1 = offset + overflow[mainSide];
    var max$1 = offset - overflow[altSide];
    var additive = tether ? -popperRect[len] / 2 : 0;
    var minLen = variation === start ? referenceRect[len] : popperRect[len];
    var maxLen = variation === start ? -popperRect[len] : -referenceRect[len]; // We need to include the arrow in the calculation so the arrow doesn't go
    // outside the reference bounds

    var arrowElement = state.elements.arrow;
    var arrowRect = tether && arrowElement ? getLayoutRect(arrowElement) : {
      width: 0,
      height: 0
    };
    var arrowPaddingObject = state.modifiersData['arrow#persistent'] ? state.modifiersData['arrow#persistent'].padding : getFreshSideObject();
    var arrowPaddingMin = arrowPaddingObject[mainSide];
    var arrowPaddingMax = arrowPaddingObject[altSide]; // If the reference length is smaller than the arrow length, we don't want
    // to include its full size in the calculation. If the reference is small
    // and near the edge of a boundary, the popper can overflow even if the
    // reference is not overflowing as well (e.g. virtual elements with no
    // width or height)

    var arrowLen = within(0, referenceRect[len], arrowRect[len]);
    var minOffset = isBasePlacement ? referenceRect[len] / 2 - additive - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis : minLen - arrowLen - arrowPaddingMin - normalizedTetherOffsetValue.mainAxis;
    var maxOffset = isBasePlacement ? -referenceRect[len] / 2 + additive + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis : maxLen + arrowLen + arrowPaddingMax + normalizedTetherOffsetValue.mainAxis;
    var arrowOffsetParent = state.elements.arrow && getOffsetParent(state.elements.arrow);
    var clientOffset = arrowOffsetParent ? mainAxis === 'y' ? arrowOffsetParent.clientTop || 0 : arrowOffsetParent.clientLeft || 0 : 0;
    var offsetModifierValue = (_offsetModifierState$ = offsetModifierState == null ? void 0 : offsetModifierState[mainAxis]) != null ? _offsetModifierState$ : 0;
    var tetherMin = offset + minOffset - offsetModifierValue - clientOffset;
    var tetherMax = offset + maxOffset - offsetModifierValue;
    var preventedOffset = within(tether ? min(min$1, tetherMin) : min$1, offset, tether ? max(max$1, tetherMax) : max$1);
    popperOffsets[mainAxis] = preventedOffset;
    data[mainAxis] = preventedOffset - offset;
  }

  if (checkAltAxis) {
    var _offsetModifierState$2;

    var _mainSide = mainAxis === 'x' ? top : left;

    var _altSide = mainAxis === 'x' ? bottom : right;

    var _offset = popperOffsets[altAxis];

    var _len = altAxis === 'y' ? 'height' : 'width';

    var _min = _offset + overflow[_mainSide];

    var _max = _offset - overflow[_altSide];

    var isOriginSide = [top, left].indexOf(basePlacement) !== -1;

    var _offsetModifierValue = (_offsetModifierState$2 = offsetModifierState == null ? void 0 : offsetModifierState[altAxis]) != null ? _offsetModifierState$2 : 0;

    var _tetherMin = isOriginSide ? _min : _offset - referenceRect[_len] - popperRect[_len] - _offsetModifierValue + normalizedTetherOffsetValue.altAxis;

    var _tetherMax = isOriginSide ? _offset + referenceRect[_len] + popperRect[_len] - _offsetModifierValue - normalizedTetherOffsetValue.altAxis : _max;

    var _preventedOffset = tether && isOriginSide ? withinMaxClamp(_tetherMin, _offset, _tetherMax) : within(tether ? _tetherMin : _min, _offset, tether ? _tetherMax : _max);

    popperOffsets[altAxis] = _preventedOffset;
    data[altAxis] = _preventedOffset - _offset;
  }

  state.modifiersData[name] = data;
} // eslint-disable-next-line import/no-unused-modules


var preventOverflow$1 = {
  name: 'preventOverflow',
  enabled: true,
  phase: 'main',
  fn: preventOverflow,
  requiresIfExists: ['offset']
};

function getHTMLElementScroll(element) {
  return {
    scrollLeft: element.scrollLeft,
    scrollTop: element.scrollTop
  };
}

function getNodeScroll(node) {
  if (node === getWindow(node) || !isHTMLElement(node)) {
    return getWindowScroll(node);
  } else {
    return getHTMLElementScroll(node);
  }
}

function isElementScaled(element) {
  var rect = element.getBoundingClientRect();
  var scaleX = round(rect.width) / element.offsetWidth || 1;
  var scaleY = round(rect.height) / element.offsetHeight || 1;
  return scaleX !== 1 || scaleY !== 1;
} // Returns the composite rect of an element relative to its offsetParent.
// Composite means it takes into account transforms as well as layout.


function getCompositeRect(elementOrVirtualElement, offsetParent, isFixed) {
  if (isFixed === void 0) {
    isFixed = false;
  }

  var isOffsetParentAnElement = isHTMLElement(offsetParent);
  var offsetParentIsScaled = isHTMLElement(offsetParent) && isElementScaled(offsetParent);
  var documentElement = getDocumentElement(offsetParent);
  var rect = getBoundingClientRect(elementOrVirtualElement, offsetParentIsScaled, isFixed);
  var scroll = {
    scrollLeft: 0,
    scrollTop: 0
  };
  var offsets = {
    x: 0,
    y: 0
  };

  if (isOffsetParentAnElement || !isOffsetParentAnElement && !isFixed) {
    if (getNodeName(offsetParent) !== 'body' || // https://github.com/popperjs/popper-core/issues/1078
    isScrollParent(documentElement)) {
      scroll = getNodeScroll(offsetParent);
    }

    if (isHTMLElement(offsetParent)) {
      offsets = getBoundingClientRect(offsetParent, true);
      offsets.x += offsetParent.clientLeft;
      offsets.y += offsetParent.clientTop;
    } else if (documentElement) {
      offsets.x = getWindowScrollBarX(documentElement);
    }
  }

  return {
    x: rect.left + scroll.scrollLeft - offsets.x,
    y: rect.top + scroll.scrollTop - offsets.y,
    width: rect.width,
    height: rect.height
  };
}

function order(modifiers) {
  var map = new Map();
  var visited = new Set();
  var result = [];
  modifiers.forEach(function (modifier) {
    map.set(modifier.name, modifier);
  }); // On visiting object, check for its dependencies and visit them recursively

  function sort(modifier) {
    visited.add(modifier.name);
    var requires = [].concat(modifier.requires || [], modifier.requiresIfExists || []);
    requires.forEach(function (dep) {
      if (!visited.has(dep)) {
        var depModifier = map.get(dep);

        if (depModifier) {
          sort(depModifier);
        }
      }
    });
    result.push(modifier);
  }

  modifiers.forEach(function (modifier) {
    if (!visited.has(modifier.name)) {
      // check for visited object
      sort(modifier);
    }
  });
  return result;
}

function orderModifiers(modifiers) {
  // order based on dependencies
  var orderedModifiers = order(modifiers); // order based on phase

  return modifierPhases.reduce(function (acc, phase) {
    return acc.concat(orderedModifiers.filter(function (modifier) {
      return modifier.phase === phase;
    }));
  }, []);
}

function debounce(fn) {
  var pending;
  return function () {
    if (!pending) {
      pending = new Promise(function (resolve) {
        Promise.resolve().then(function () {
          pending = undefined;
          resolve(fn());
        });
      });
    }

    return pending;
  };
}

function format(str) {
  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    args[_key - 1] = arguments[_key];
  }

  return [].concat(args).reduce(function (p, c) {
    return p.replace(/%s/, c);
  }, str);
}

var INVALID_MODIFIER_ERROR = 'Popper: modifier "%s" provided an invalid %s property, expected %s but got %s';
var MISSING_DEPENDENCY_ERROR = 'Popper: modifier "%s" requires "%s", but "%s" modifier is not available';
var VALID_PROPERTIES = ['name', 'enabled', 'phase', 'fn', 'effect', 'requires', 'options'];
function validateModifiers(modifiers) {
  modifiers.forEach(function (modifier) {
    [].concat(Object.keys(modifier), VALID_PROPERTIES) // IE11-compatible replacement for `new Set(iterable)`
    .filter(function (value, index, self) {
      return self.indexOf(value) === index;
    }).forEach(function (key) {
      switch (key) {
        case 'name':
          if (typeof modifier.name !== 'string') {
            console.error(format(INVALID_MODIFIER_ERROR, String(modifier.name), '"name"', '"string"', "\"" + String(modifier.name) + "\""));
          }

          break;

        case 'enabled':
          if (typeof modifier.enabled !== 'boolean') {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"enabled"', '"boolean"', "\"" + String(modifier.enabled) + "\""));
          }

          break;

        case 'phase':
          if (modifierPhases.indexOf(modifier.phase) < 0) {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"phase"', "either " + modifierPhases.join(', '), "\"" + String(modifier.phase) + "\""));
          }

          break;

        case 'fn':
          if (typeof modifier.fn !== 'function') {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"fn"', '"function"', "\"" + String(modifier.fn) + "\""));
          }

          break;

        case 'effect':
          if (modifier.effect != null && typeof modifier.effect !== 'function') {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"effect"', '"function"', "\"" + String(modifier.fn) + "\""));
          }

          break;

        case 'requires':
          if (modifier.requires != null && !Array.isArray(modifier.requires)) {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"requires"', '"array"', "\"" + String(modifier.requires) + "\""));
          }

          break;

        case 'requiresIfExists':
          if (!Array.isArray(modifier.requiresIfExists)) {
            console.error(format(INVALID_MODIFIER_ERROR, modifier.name, '"requiresIfExists"', '"array"', "\"" + String(modifier.requiresIfExists) + "\""));
          }

          break;

        case 'options':
        case 'data':
          break;

        default:
          console.error("PopperJS: an invalid property has been provided to the \"" + modifier.name + "\" modifier, valid properties are " + VALID_PROPERTIES.map(function (s) {
            return "\"" + s + "\"";
          }).join(', ') + "; but \"" + key + "\" was provided.");
      }

      modifier.requires && modifier.requires.forEach(function (requirement) {
        if (modifiers.find(function (mod) {
          return mod.name === requirement;
        }) == null) {
          console.error(format(MISSING_DEPENDENCY_ERROR, String(modifier.name), requirement, requirement));
        }
      });
    });
  });
}

function uniqueBy(arr, fn) {
  var identifiers = new Set();
  return arr.filter(function (item) {
    var identifier = fn(item);

    if (!identifiers.has(identifier)) {
      identifiers.add(identifier);
      return true;
    }
  });
}

function mergeByName(modifiers) {
  var merged = modifiers.reduce(function (merged, current) {
    var existing = merged[current.name];
    merged[current.name] = existing ? Object.assign({}, existing, current, {
      options: Object.assign({}, existing.options, current.options),
      data: Object.assign({}, existing.data, current.data)
    }) : current;
    return merged;
  }, {}); // IE11 does not support Object.values

  return Object.keys(merged).map(function (key) {
    return merged[key];
  });
}

var INVALID_ELEMENT_ERROR = 'Popper: Invalid reference or popper argument provided. They must be either a DOM element or virtual element.';
var INFINITE_LOOP_ERROR = 'Popper: An infinite loop in the modifiers cycle has been detected! The cycle has been interrupted to prevent a browser crash.';
var DEFAULT_OPTIONS = {
  placement: 'bottom',
  modifiers: [],
  strategy: 'absolute'
};

function areValidElements() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }

  return !args.some(function (element) {
    return !(element && typeof element.getBoundingClientRect === 'function');
  });
}

function popperGenerator(generatorOptions) {
  if (generatorOptions === void 0) {
    generatorOptions = {};
  }

  var _generatorOptions = generatorOptions,
      _generatorOptions$def = _generatorOptions.defaultModifiers,
      defaultModifiers = _generatorOptions$def === void 0 ? [] : _generatorOptions$def,
      _generatorOptions$def2 = _generatorOptions.defaultOptions,
      defaultOptions = _generatorOptions$def2 === void 0 ? DEFAULT_OPTIONS : _generatorOptions$def2;
  return function createPopper(reference, popper, options) {
    if (options === void 0) {
      options = defaultOptions;
    }

    var state = {
      placement: 'bottom',
      orderedModifiers: [],
      options: Object.assign({}, DEFAULT_OPTIONS, defaultOptions),
      modifiersData: {},
      elements: {
        reference: reference,
        popper: popper
      },
      attributes: {},
      styles: {}
    };
    var effectCleanupFns = [];
    var isDestroyed = false;
    var instance = {
      state: state,
      setOptions: function setOptions(setOptionsAction) {
        var options = typeof setOptionsAction === 'function' ? setOptionsAction(state.options) : setOptionsAction;
        cleanupModifierEffects();
        state.options = Object.assign({}, defaultOptions, state.options, options);
        state.scrollParents = {
          reference: isElement(reference) ? listScrollParents(reference) : reference.contextElement ? listScrollParents(reference.contextElement) : [],
          popper: listScrollParents(popper)
        }; // Orders the modifiers based on their dependencies and `phase`
        // properties

        var orderedModifiers = orderModifiers(mergeByName([].concat(defaultModifiers, state.options.modifiers))); // Strip out disabled modifiers

        state.orderedModifiers = orderedModifiers.filter(function (m) {
          return m.enabled;
        }); // Validate the provided modifiers so that the consumer will get warned
        // if one of the modifiers is invalid for any reason

        if (process.env.NODE_ENV !== "production") {
          var modifiers = uniqueBy([].concat(orderedModifiers, state.options.modifiers), function (_ref) {
            var name = _ref.name;
            return name;
          });
          validateModifiers(modifiers);

          if (getBasePlacement(state.options.placement) === auto) {
            var flipModifier = state.orderedModifiers.find(function (_ref2) {
              var name = _ref2.name;
              return name === 'flip';
            });

            if (!flipModifier) {
              console.error(['Popper: "auto" placements require the "flip" modifier be', 'present and enabled to work.'].join(' '));
            }
          }

          var _getComputedStyle = getComputedStyle(popper),
              marginTop = _getComputedStyle.marginTop,
              marginRight = _getComputedStyle.marginRight,
              marginBottom = _getComputedStyle.marginBottom,
              marginLeft = _getComputedStyle.marginLeft; // We no longer take into account `margins` on the popper, and it can
          // cause bugs with positioning, so we'll warn the consumer


          if ([marginTop, marginRight, marginBottom, marginLeft].some(function (margin) {
            return parseFloat(margin);
          })) {
            console.warn(['Popper: CSS "margin" styles cannot be used to apply padding', 'between the popper and its reference element or boundary.', 'To replicate margin, use the `offset` modifier, as well as', 'the `padding` option in the `preventOverflow` and `flip`', 'modifiers.'].join(' '));
          }
        }

        runModifierEffects();
        return instance.update();
      },
      // Sync update – it will always be executed, even if not necessary. This
      // is useful for low frequency updates where sync behavior simplifies the
      // logic.
      // For high frequency updates (e.g. `resize` and `scroll` events), always
      // prefer the async Popper#update method
      forceUpdate: function forceUpdate() {
        if (isDestroyed) {
          return;
        }

        var _state$elements = state.elements,
            reference = _state$elements.reference,
            popper = _state$elements.popper; // Don't proceed if `reference` or `popper` are not valid elements
        // anymore

        if (!areValidElements(reference, popper)) {
          if (process.env.NODE_ENV !== "production") {
            console.error(INVALID_ELEMENT_ERROR);
          }

          return;
        } // Store the reference and popper rects to be read by modifiers


        state.rects = {
          reference: getCompositeRect(reference, getOffsetParent(popper), state.options.strategy === 'fixed'),
          popper: getLayoutRect(popper)
        }; // Modifiers have the ability to reset the current update cycle. The
        // most common use case for this is the `flip` modifier changing the
        // placement, which then needs to re-run all the modifiers, because the
        // logic was previously ran for the previous placement and is therefore
        // stale/incorrect

        state.reset = false;
        state.placement = state.options.placement; // On each update cycle, the `modifiersData` property for each modifier
        // is filled with the initial data specified by the modifier. This means
        // it doesn't persist and is fresh on each update.
        // To ensure persistent data, use `${name}#persistent`

        state.orderedModifiers.forEach(function (modifier) {
          return state.modifiersData[modifier.name] = Object.assign({}, modifier.data);
        });
        var __debug_loops__ = 0;

        for (var index = 0; index < state.orderedModifiers.length; index++) {
          if (process.env.NODE_ENV !== "production") {
            __debug_loops__ += 1;

            if (__debug_loops__ > 100) {
              console.error(INFINITE_LOOP_ERROR);
              break;
            }
          }

          if (state.reset === true) {
            state.reset = false;
            index = -1;
            continue;
          }

          var _state$orderedModifie = state.orderedModifiers[index],
              fn = _state$orderedModifie.fn,
              _state$orderedModifie2 = _state$orderedModifie.options,
              _options = _state$orderedModifie2 === void 0 ? {} : _state$orderedModifie2,
              name = _state$orderedModifie.name;

          if (typeof fn === 'function') {
            state = fn({
              state: state,
              options: _options,
              name: name,
              instance: instance
            }) || state;
          }
        }
      },
      // Async and optimistically optimized update – it will not be executed if
      // not necessary (debounced to run at most once-per-tick)
      update: debounce(function () {
        return new Promise(function (resolve) {
          instance.forceUpdate();
          resolve(state);
        });
      }),
      destroy: function destroy() {
        cleanupModifierEffects();
        isDestroyed = true;
      }
    };

    if (!areValidElements(reference, popper)) {
      if (process.env.NODE_ENV !== "production") {
        console.error(INVALID_ELEMENT_ERROR);
      }

      return instance;
    }

    instance.setOptions(options).then(function (state) {
      if (!isDestroyed && options.onFirstUpdate) {
        options.onFirstUpdate(state);
      }
    }); // Modifiers have the ability to execute arbitrary code before the first
    // update cycle runs. They will be executed in the same order as the update
    // cycle. This is useful when a modifier adds some persistent data that
    // other modifiers need to use, but the modifier is run after the dependent
    // one.

    function runModifierEffects() {
      state.orderedModifiers.forEach(function (_ref3) {
        var name = _ref3.name,
            _ref3$options = _ref3.options,
            options = _ref3$options === void 0 ? {} : _ref3$options,
            effect = _ref3.effect;

        if (typeof effect === 'function') {
          var cleanupFn = effect({
            state: state,
            name: name,
            instance: instance,
            options: options
          });

          var noopFn = function noopFn() {};

          effectCleanupFns.push(cleanupFn || noopFn);
        }
      });
    }

    function cleanupModifierEffects() {
      effectCleanupFns.forEach(function (fn) {
        return fn();
      });
      effectCleanupFns = [];
    }

    return instance;
  };
}

var defaultModifiers = [eventListeners, popperOffsets$1, computeStyles$1, applyStyles$1, offset$1, flip$1, preventOverflow$1, arrow$1, hide$1];
var createPopper = /*#__PURE__*/popperGenerator({
  defaultModifiers: defaultModifiers
}); // eslint-disable-next-line import/no-unused-modules

const wrapAround = (value, size) => {
    return ((value % size) + size) % size;
};
class Suggest {
    constructor(owner, containerEl, scope) {
        this.owner = owner;
        this.containerEl = containerEl;
        containerEl.on("click", ".suggestion-item", this.onSuggestionClick.bind(this));
        containerEl.on("mousemove", ".suggestion-item", this.onSuggestionMouseover.bind(this));
        scope.register([], "ArrowUp", event => {
            if (!event.isComposing) {
                this.setSelectedItem(this.selectedItem - 1, true);
                return false;
            }
        });
        scope.register([], "ArrowDown", event => {
            if (!event.isComposing) {
                this.setSelectedItem(this.selectedItem + 1, true);
                return false;
            }
        });
        scope.register([], "Enter", event => {
            if (!event.isComposing) {
                this.useSelectedItem(event);
                return false;
            }
        });
    }
    onSuggestionClick(event, el) {
        event.preventDefault();
        const item = this.suggestions.indexOf(el);
        this.setSelectedItem(item, false);
        this.useSelectedItem(event);
    }
    onSuggestionMouseover(_event, el) {
        const item = this.suggestions.indexOf(el);
        this.setSelectedItem(item, false);
    }
    setSuggestions(values) {
        this.containerEl.empty();
        const suggestionEls = [];
        values.forEach(value => {
            const suggestionEl = this.containerEl.createDiv("suggestion-item");
            this.owner.renderSuggestion(value, suggestionEl);
            suggestionEls.push(suggestionEl);
        });
        this.values = values;
        this.suggestions = suggestionEls;
        this.setSelectedItem(0, false);
    }
    useSelectedItem(event) {
        const currentValue = this.values[this.selectedItem];
        if (currentValue) {
            this.owner.selectSuggestion(currentValue, event);
        }
    }
    setSelectedItem(selectedIndex, scrollIntoView) {
        const normalizedIndex = wrapAround(selectedIndex, this.suggestions.length);
        const prevSelectedSuggestion = this.suggestions[this.selectedItem];
        const selectedSuggestion = this.suggestions[normalizedIndex];
        prevSelectedSuggestion === null || prevSelectedSuggestion === void 0 ? void 0 : prevSelectedSuggestion.removeClass("is-selected");
        selectedSuggestion === null || selectedSuggestion === void 0 ? void 0 : selectedSuggestion.addClass("is-selected");
        this.selectedItem = normalizedIndex;
        if (scrollIntoView) {
            selectedSuggestion.scrollIntoView(false);
        }
    }
}
class TextInputSuggest {
    constructor(app, inputEl) {
        this.app = app;
        this.inputEl = inputEl;
        this.scope = new obsidian.Scope();
        this.suggestEl = createDiv("suggestion-container");
        const suggestion = this.suggestEl.createDiv("suggestion");
        this.suggest = new Suggest(this, suggestion, this.scope);
        this.scope.register([], "Escape", this.close.bind(this));
        this.inputEl.addEventListener("input", this.onInputChanged.bind(this));
        this.inputEl.addEventListener("focus", this.onInputChanged.bind(this));
        this.inputEl.addEventListener("blur", this.close.bind(this));
        this.suggestEl.on("mousedown", ".suggestion-container", (event) => {
            event.preventDefault();
        });
    }
    onInputChanged() {
        const inputStr = this.inputEl.value;
        const suggestions = this.getSuggestions(inputStr);
        if (suggestions.length > 0) {
            this.suggest.setSuggestions(suggestions);
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            this.open(this.app.dom.appContainerEl, this.inputEl);
        }
    }
    open(container, inputEl) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        this.app.keymap.pushScope(this.scope);
        container.appendChild(this.suggestEl);
        this.popper = createPopper(inputEl, this.suggestEl, {
            placement: "bottom-start",
            modifiers: [
                {
                    name: "sameWidth",
                    enabled: true,
                    fn: ({ state, instance }) => {
                        // Note: positioning needs to be calculated twice -
                        // first pass - positioning it according to the width of the popper
                        // second pass - position it with the width bound to the reference element
                        // we need to early exit to avoid an infinite loop
                        const targetWidth = `${state.rects.reference.width}px`;
                        if (state.styles.popper.width === targetWidth) {
                            return;
                        }
                        state.styles.popper.width = targetWidth;
                        instance.update();
                    },
                    phase: "beforeWrite",
                    requires: ["computeStyles"],
                },
            ],
        });
    }
    close() {
        var _a, _b, _c;
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        this.app.keymap.popScope(this.scope);
        (_a = this.suggest) === null || _a === void 0 ? void 0 : _a.setSuggestions([]);
        (_b = this.popper) === null || _b === void 0 ? void 0 : _b.destroy();
        (_c = this.suggestEl) === null || _c === void 0 ? void 0 : _c.detach();
    }
}

class FileSuggest extends TextInputSuggest {
    getSuggestions(inputStr) {
        const abstractFiles = this.app.vault.getAllLoadedFiles();
        const files = [];
        const lowerCaseInputStr = inputStr.toLowerCase();
        abstractFiles.forEach((file) => {
            if (file instanceof obsidian.TFile && file.extension === "md" && file.path.toLowerCase().contains(lowerCaseInputStr)) {
                files.push(file);
            }
        });
        return files;
    }
    renderSuggestion(file, el) {
        el.setText(file.path);
    }
    selectSuggestion(file) {
        this.inputEl.value = file.path;
        this.inputEl.trigger("input");
        this.close();
    }
}

const DEFAULT_SETTINGS = {
    showInstructions: true,
    showDeletePrompt: true,
    saveOnSwitch: false,
    saveOnChange: false,
    workspaceSettings: false,
    systemDarkMode: false,
    globalSettings: {},
    activeWorkspaceDesktop: "",
    activeWorkspaceMobile: "",
    reloadLivePreview: false,
    workspaceSwitcherRibbon: false,
    modeSwitcherRibbon: false,
    replaceNativeRibbon: false,
};
function getChildIds(split, leafs = null) {
    // recursive function to get metadata from all leafs in a workspace split
    if (!leafs)
        leafs = [];
    if (split.type == "leaf") {
        leafs.push({ id: split.id, file: split.state.state.file, mode: split.state.state.mode });
    }
    else if (split.type == "split") {
        split.children.forEach((child) => {
            getChildIds(child, leafs);
        });
    }
    return leafs;
}
class WorkspacesPlusSettingsTab extends obsidian.PluginSettingTab {
    constructor(app, plugin) {
        super(app, plugin);
        this.plugin = plugin;
    }
    display() {
        const { containerEl } = this;
        containerEl.empty();
        if (!this.plugin.utils.isNativePluginEnabled) {
            containerEl.createEl("h2", {
                text: "Please enable the Workspaces plugin under Core Plugins before using this plugin",
            });
            return;
        }
        // containerEl.createEl("h2", { text: "Workspaces Plus" });
        containerEl.createEl("h2", {
            text: "Quick Switcher Settings",
        });
        new obsidian.Setting(containerEl)
            .setName("Show instructions")
            .setDesc(`Show available keyboard shortcuts at the bottom of the workspace quick switcher`)
            .addToggle(toggle => toggle.setValue(this.plugin.settings.showInstructions).onChange(value => {
            this.plugin.settings.showInstructions = value;
            this.plugin.saveData(this.plugin.settings);
        }));
        new obsidian.Setting(containerEl)
            .setName("Show workspace delete confirmation")
            .setDesc(`Show a confirmation prompt on workspace deletion`)
            .addToggle(toggle => toggle.setValue(this.plugin.settings.showDeletePrompt).onChange(value => {
            this.plugin.settings.showDeletePrompt = value;
            this.plugin.saveData(this.plugin.settings);
        }));
        new obsidian.Setting(containerEl)
            .setName("Show Workspace Sidebar Ribbon Icon")
            // .setDesc(``)
            .addToggle(toggle => toggle.setValue(this.plugin.settings.workspaceSwitcherRibbon).onChange(value => {
            this.plugin.settings.workspaceSwitcherRibbon = value;
            this.plugin.saveData(this.plugin.settings);
            this.plugin.toggleWorkspaceRibbonButton();
        }));
        new obsidian.Setting(containerEl)
            .setName("Hide the native Workspace Sidebar Ribbon Icon")
            // .setDesc(``)
            .addToggle(toggle => toggle.setValue(this.plugin.settings.replaceNativeRibbon).onChange(value => {
            this.plugin.settings.replaceNativeRibbon = value;
            this.plugin.saveData(this.plugin.settings);
            this.plugin.toggleNativeWorkspaceRibbon();
        }));
        new obsidian.Setting(containerEl)
            .setName("Show Workspace Mode Sidebar Ribbon Icon")
            // .setDesc(``)
            .addToggle(toggle => toggle.setValue(this.plugin.settings.modeSwitcherRibbon).onChange(value => {
            this.plugin.settings.modeSwitcherRibbon = value;
            this.plugin.saveData(this.plugin.settings);
            this.plugin.toggleModeRibbonButton();
        }));
        containerEl.createEl("h2", {
            text: "Workspace Enhancements",
        });
        new obsidian.Setting(containerEl)
            .setName(createFragment(function (e) {
            e.appendText("Workspace Modes"),
                e.createSpan({
                    cls: "flair mod-pop",
                    text: "beta",
                });
        }))
            .setDesc(`Modes are a new type of Workspace that store all of the native Obsidian Editor, Files & Links, 
        and Appearance settings. Enabling this will add a new mode switcher to the status bar that will allow you
        to save, apply, rename, and switch between modes.`)
            .then(setting => {
            setting.settingEl.addClass("workspace-modes");
            if (this.plugin.settings.workspaceSettings)
                setting.settingEl.addClass("is-enabled");
            else
                setting.settingEl.removeClass("is-enabled");
            setting.addToggle(toggle => toggle.setValue(this.plugin.settings.workspaceSettings).onChange(value => {
                if (value)
                    setting.settingEl.addClass("is-enabled");
                else
                    setting.settingEl.removeClass("is-enabled");
                this.plugin.settings.workspaceSettings = value;
                this.plugin.saveData(this.plugin.settings);
                if (value)
                    this.plugin.enableModesFeature();
                else
                    this.plugin.disableModesFeature();
            }));
        });
        new obsidian.Setting(containerEl)
            .setName("Auto save the current workspace on layout change")
            .setDesc(`This option will auto save your current workspace on any layout change.
         Leave this disabled if you want full control over when your workspace is saved.`)
            .addToggle(toggle => toggle.setValue(this.plugin.settings.saveOnChange).onChange(value => {
            this.plugin.settings.saveOnChange = value;
            this.plugin.saveData(this.plugin.settings);
        }));
        new obsidian.Setting(containerEl)
            .setName("Respect system dark mode setting")
            .setClass("requires-workspace-modes")
            .setDesc(`Let the OS determine the light/dark mode setting when switching modes. This setting can only be used if Workspace Modes is enabled.`)
            .addToggle(toggle => toggle.setValue(this.plugin.settings.systemDarkMode).onChange(value => {
            this.plugin.settings.systemDarkMode = value;
            this.plugin.saveData(this.plugin.settings);
        }));
        new obsidian.Setting(containerEl)
            .setName("Automatically reload Obsidian on Live Preview setting change")
            .setClass("requires-workspace-modes")
            .setDesc(`When switching between Modes with different Experimental Live Preview settings, reload Obsidian in order for the setting
                change to take effect. ⚠️Note: Obsidian will reload automatically after changing workspaces, if needed, without any prompts.`)
            .addToggle(toggle => toggle.setValue(this.plugin.settings.reloadLivePreview).onChange(value => {
            this.plugin.settings.reloadLivePreview = value;
            this.plugin.saveData(this.plugin.settings);
        }));
        containerEl.createEl("h2", {
            text: "Per Workspace Settings",
        });
        let { workspaces } = this.plugin.workspacePlugin;
        Object.entries(workspaces).forEach(entry => {
            const [workspaceName, workspace] = entry;
            const workspaceSettings = this.plugin.utils.getWorkspaceSettings(workspaceName);
            if (this.plugin.utils.isMode(workspaceName))
                return;
            // containerEl.createEl("h3", {
            //   text: workspaceName,
            // });
            new obsidian.Setting(containerEl)
                .setHeading()
                .setClass("settings-heading")
                .setName(workspaceName)
                .then(setting => {
                setting.settingEl.addClass("is-collapsed");
                const iconContainer = createSpan({
                    cls: "settings-collapse-indicator",
                });
                obsidian.setIcon(iconContainer, "right-triangle");
                setting.nameEl.prepend(iconContainer);
                setting.settingEl.addEventListener("click", e => {
                    setting.settingEl.toggleClass("is-collapsed", !setting.settingEl.hasClass("is-collapsed"));
                });
            });
            const subContainerEL = containerEl.createDiv({ cls: "settings-container" });
            new obsidian.Setting(subContainerEL).setName("Workspace Description").addText(textfield => {
                var _a;
                textfield.inputEl.type = "text";
                (_a = textfield.inputEl.parentElement) === null || _a === void 0 ? void 0 : _a.addClass("search-input-container");
                textfield.setValue(String((workspaceSettings === null || workspaceSettings === void 0 ? void 0 : workspaceSettings.description) || ""));
                textfield.onChange(value => {
                    workspaceSettings.description = value;
                    this.plugin.workspacePlugin.saveData();
                });
            });
            // new Setting(containerEl)
            //   .setName(`Auto save workspace on changes (not yet implemented)`)
            //   // .setDesc(``)
            //   .addToggle(toggle =>
            //     toggle.setValue(workspaceSettings?.autoSave).onChange(value => {
            //       workspaceSettings.autoSave = value;
            //       this.plugin.workspacePlugin.saveData();
            //     })
            //   );
            new obsidian.Setting(subContainerEL).setHeading().setName("File Overrides");
            getChildIds(workspace.main).forEach((leaf) => {
                let currentFile;
                if (workspaceSettings.fileOverrides && workspaceSettings.fileOverrides[leaf.id]) {
                    currentFile = workspaceSettings.fileOverrides[leaf.id];
                }
                else {
                    currentFile = null;
                }
                new obsidian.Setting(subContainerEL)
                    .setName(leaf.id ? leaf.id : "unknown")
                    .setClass("file-override")
                    .addSearch(cb => {
                    new FileSuggest(this.app, cb.inputEl);
                    cb.setPlaceholder(leaf.file ? leaf.file : "");
                    if (currentFile)
                        cb.setValue(currentFile);
                    // TODO: Allow for assigning names to pane IDs
                    cb.onChange(overrideFile => {
                        // store leaf ID and filename override to workspace settings
                        // the workspace load function will look for overrides and apply them
                        // need to create a function that can search for a leaf id and update it
                        if (!workspaceSettings.fileOverrides)
                            workspaceSettings.fileOverrides = {};
                        if (overrideFile)
                            workspaceSettings.fileOverrides[leaf.id] = overrideFile;
                        else
                            delete workspaceSettings.fileOverrides[leaf.id];
                    });
                });
            });
        });
        containerEl
            .createEl("h2", {
            text: "Per Mode Settings",
        })
            .addClass("requires-workspace-modes");
        Object.entries(workspaces).forEach(entry => {
            const [modeName, mode] = entry;
            if (!this.plugin.utils.isMode(modeName))
                return;
            const modeSettings = this.plugin.utils.getModeSettings(modeName);
            new obsidian.Setting(containerEl)
                .setHeading()
                .setClass("settings-heading")
                .setClass("requires-workspace-modes")
                .setName(modeName === null || modeName === void 0 ? void 0 : modeName.replace(/^mode: /i, ""))
                .then(setting => {
                setting.settingEl.addClass("is-collapsed");
                const iconContainer = createSpan({
                    cls: "settings-collapse-indicator",
                });
                obsidian.setIcon(iconContainer, "right-triangle");
                setting.nameEl.prepend(iconContainer);
                setting.settingEl.addEventListener("click", e => {
                    setting.settingEl.toggleClass("is-collapsed", !setting.settingEl.hasClass("is-collapsed"));
                });
            });
            const subContainerEL = containerEl.createDiv({ cls: "settings-container" });
            new obsidian.Setting(subContainerEL)
                .setName(`Save and load left/right sidebar state`)
                .setClass("requires-workspace-modes")
                // .setDesc(``)
                .addToggle(toggle => toggle.setValue(modeSettings === null || modeSettings === void 0 ? void 0 : modeSettings.saveSidebar).onChange(value => {
                modeSettings.saveSidebar = value;
                this.plugin.workspacePlugin.saveData();
            }));
        });
    }
}
// setting.settingEl.addEventListener("click", (e) => {
//   setting.settingEl.toggleClass(
//     "is-collapsed",
//     !setting.settingEl.hasClass("is-collapsed")
//   );
// });

class ConfirmationModal extends obsidian.Modal {
    constructor(app, config) {
        super(app);
        this.modalEl.addClass("workspace-delete-confirm-modal");
        const { cta, onAccept, text, title } = config;
        this.contentEl.createEl("h3", { text: title });
        let e = this.contentEl.createEl("p", { text });
        e.id = "workspace-delete-confirm-dialog";
        this.contentEl.createDiv("modal-button-container", buttonsEl => {
            buttonsEl.createEl("button", { text: "Cancel" }).addEventListener("click", () => this.close());
            const btnSumbit = buttonsEl.createEl("button", {
                attr: { type: "submit" },
                cls: "mod-cta",
                text: cta,
            });
            btnSumbit.addEventListener("click", (e) => __awaiter(this, void 0, void 0, function* () {
                yield onAccept();
                this.close();
            }));
            setTimeout(() => {
                btnSumbit.focus();
            }, 50);
        });
    }
}
function createConfirmationDialog(app, { cta, onAccept, text, title }) {
    new ConfirmationModal(app, { cta, onAccept, text, title }).open();
}

const SETTINGS_ATTR$1 = "workspaces-plus:settings-v1";
class WorkspacesPlusPluginWorkspaceModal extends obsidian.FuzzySuggestModal {
    constructor(plugin, settings, hotkey = false) {
        super(plugin.app);
        this.showInstructions = false;
        this.emptyStateText = "No match found.";
        this.onSuggestionClick = function (evt, itemEl) {
            if (itemEl.contentEditable === "true") {
                // allow cursor selection in rename mode by ignoring the click
                evt.stopPropagation();
                return;
            }
            evt.preventDefault();
            let item = this.chooser.suggestions.indexOf(itemEl);
            this.chooser.setSelectedItem(item), this.useSelectedItem(evt);
        };
        this.onSuggestionMouseover = function (evt, itemEl) {
            let item = this.chooser.suggestions.indexOf(itemEl);
            this.chooser.setSelectedItem(item);
        };
        this.useSelectedItem = function (evt) {
            const targetEl = evt.composedPath()[0];
            if (targetEl.contentEditable === "true") {
                this.handleRename(targetEl);
                return;
            }
            let workspaceName = this.inputEl.value ? this.inputEl.value : this.chooser.values[this.chooser.selectedItem].item;
            if (!this.values && workspaceName && evt.shiftKey) {
                this.saveAndStay();
                // if (!/^mode:/i.test(workspaceName)) this.setWorkspace(workspaceName);
                // this.close();
                return false;
            }
            else if (!this.chooser.values)
                return false;
            let item = this.chooser.values ? this.chooser.values[this.chooser.selectedItem] : workspaceName;
            return void 0 !== item && (this.selectSuggestion(item, evt), true);
        };
        this.onRenameClick = function (evt, el) {
            evt.stopPropagation();
            if (!el)
                el = this.chooser.suggestions[this.chooser.selectedItem];
            el.parentElement.parentElement.addClass("renaming");
            if (el.contentEditable === "true") {
                el.textContent = el.dataset.workspaceName;
                el.contentEditable = "false";
                return;
            }
            else {
                el.contentEditable = "true";
            }
            const selection = window.getSelection();
            const range = document.createRange();
            selection.removeAllRanges();
            range.selectNodeContents(el);
            range.collapse(false);
            selection.addRange(range);
            el.focus();
            el.onblur = ev => {
                el.parentElement.parentElement.removeClass("renaming");
                el.contentEditable = "false";
            };
        };
        this.app = plugin.app;
        this.plugin = plugin;
        // standard initialization
        this.settings = settings;
        this.invokedViaHotkey = hotkey;
        this.workspacePlugin = this.app.internalPlugins.getPluginById("workspaces").instance;
        this.setPlaceholder("Type workspace name...");
        this.buildInstructions();
        // temporary styling to force a transparent modal background to address certain themes
        // that apply a background to the modal container instead of the modal-bg
        this.bgEl.parentElement.setAttribute("style", "background-color: transparent !important");
        this.modalEl.classList.add("workspaces-plus-modal");
        // handle custom modal positioning when invoked via the status bar
        if (!this.invokedViaHotkey) {
            this.bgEl.setAttribute("style", "background-color: transparent");
            this.modalEl.classList.add("quick-switch");
        }
        // setup key bindings
        this.scope = new obsidian.Scope();
        this.setupScope.apply(this);
        // setup event listeners
        this.modalEl.on("input", ".prompt-input", this.onInputChanged.bind(this));
        this.modalEl.on("click", ".workspace-item", this.onSuggestionClick.bind(this));
        this.modalEl.on("mousemove", ".workspace-item", this.onSuggestionMouseover.bind(this));
        // clone the input element as a hacky way to get rid of the obsidian onInput handler
        const inputElClone = this.inputEl.cloneNode();
        // this.modalEl.replaceChild(inputElClone, this.inputEl);
        this.inputEl = inputElClone;
    }
    onNoSuggestion() {
        this.chooser.setSuggestions(null);
        this.chooser.addMessage(this.emptyStateText);
        const el = this.chooser.containerEl.querySelector(".suggestion-empty");
        el.createEl("button", {
            cls: "list-item-part",
            text: "Save as new workspace",
        }).addEventListener("click", this.saveAndStay.bind(this));
    }
    setupScope() {
        this.scope.register([], "Escape", evt => this.onEscape(evt));
        this.scope.register([], "Enter", evt => this.useSelectedItem(evt));
        this.scope.register(["Shift"], "Delete", this.deleteWorkspace.bind(this));
        this.scope.register(["Ctrl"], "Enter", evt => this.onRenameClick(evt, null));
        this.scope.register(["Shift"], "Enter", evt => this.useSelectedItem(evt));
        this.scope.register(["Alt"], "Enter", evt => this.useSelectedItem(evt));
        this.scope.register([], "ArrowUp", evt => {
            if (!evt.isComposing)
                return this.chooser.setSelectedItem(this.chooser.selectedItem - 1, true), false;
        });
        this.scope.register([], "ArrowDown", evt => {
            if (!evt.isComposing)
                return this.chooser.setSelectedItem(this.chooser.selectedItem + 1, true), false;
        });
    }
    buildInstructions() {
        if (this.settings.showInstructions || this.invokedViaHotkey) {
            let instructions;
            if (!this.settings.saveOnChange) {
                instructions = [
                    {
                        command: "shift ↵",
                        purpose: "save",
                    },
                    {
                        command: "alt ↵",
                        purpose: "save and switch",
                    },
                ];
            }
            else {
                instructions = [
                    {
                        command: "↵",
                        purpose: "switch",
                    },
                ];
            }
            instructions.push({
                command: "ctrl ↵",
                purpose: "rename",
            }, {
                command: "shift ⌫",
                purpose: "delete",
            }, {
                command: "esc",
                purpose: "cancel",
            });
            this.setInstructions(instructions);
        }
    }
    onInputChanged() {
        this.chooser.chooser.updateSuggestions();
    }
    onEscape(evt) {
        const evtTargetEl = evt.target;
        // if we're actively renaming a workspace, escape out of the rename
        if (evtTargetEl.classList.contains("workspace-item") && evtTargetEl.contentEditable === "true") {
            evtTargetEl.textContent = evtTargetEl.dataset.workspaceName;
            evtTargetEl.contentEditable = "false";
            return;
        }
        // otherwise, close the modal
        this.close();
    }
    open() {
        this.app.keymap.pushScope(this.scope);
        document.body.appendChild(this.containerEl);
        if (!this.invokedViaHotkey) {
            this.popper = createPopper(document.body.querySelector(".plugin-workspaces-plus"), this.modalEl, {
                placement: "top-start",
                modifiers: [{ name: "offset", options: { offset: [0, 10] } }],
            });
        }
        this.onOpen();
        this.app.workspace.pushClosable(this);
    }
    onOpen() {
        var _a;
        super.onOpen();
        this.activeWorkspace = this.workspacePlugin.activeWorkspace;
        let selectedIdx = this.getItems().findIndex(workspace => workspace === this.activeWorkspace);
        this.chooser.setSelectedItem(selectedIdx);
        (_a = this.chooser.suggestions[this.chooser.selectedItem]) === null || _a === void 0 ? void 0 : _a.scrollIntoViewIfNeeded();
    }
    onClose() {
        this.app.keymap.popScope(this.scope);
        super.onClose();
    }
    handleRename(targetEl) {
        targetEl.parentElement.parentElement.removeClass("renaming");
        const originalName = targetEl.dataset.workspaceName;
        const newName = targetEl.textContent;
        this.workspacePlugin.workspaces[newName] = this.workspacePlugin.workspaces[originalName];
        delete this.workspacePlugin.workspaces[originalName];
        if (originalName === this.activeWorkspace) {
            this.setWorkspace(newName);
            this.activeWorkspace = newName;
        }
        this.chooser.chooser.updateSuggestions();
        targetEl.contentEditable = "false";
        let selectedIdx = this.getItems().findIndex((workspace) => workspace === newName);
        this.chooser.setSelectedItem(selectedIdx, true);
        this.app.workspace.trigger("workspace-rename", newName, originalName);
    }
    saveAndStay() {
        let workspaceName = this.inputEl.value ? this.inputEl.value : this.chooser.values[this.chooser.selectedItem].item;
        this.workspacePlugin.saveWorkspace(workspaceName);
        this.chooser.chooser.updateSuggestions();
        if (!/^mode:/i.test(workspaceName))
            this.setWorkspace(workspaceName);
        new obsidian.Notice("Successfully saved workspace: " + workspaceName);
        this.close();
    }
    saveAndSwitch() {
        this.workspacePlugin.saveWorkspace(this.activeWorkspace);
        this.plugin.registerWorkspaceHotkeys();
        new obsidian.Notice("Successfully saved workspace: " + this.activeWorkspace);
    }
    deleteWorkspace(workspaceName = null) {
        if (!workspaceName) {
            let currentSelection = this.chooser.selectedItem;
            workspaceName = this.chooser.values[currentSelection].item;
        }
        if (this.settings.showDeletePrompt) {
            createConfirmationDialog(this.app, {
                cta: "Delete",
                onAccept: () => __awaiter(this, void 0, void 0, function* () {
                    this.doDelete(workspaceName);
                }),
                text: `Do you really want to delete the '` + workspaceName + `' workspace?`,
                title: "Workspace Delete Confirmation",
            });
        }
        else {
            this.doDelete(workspaceName);
        }
    }
    renderSuggestion(item, el) {
        super.renderSuggestion(item, el);
        const workspaceName = el.textContent;
        const resultEl = document.body.querySelector("div.workspaces-plus-modal div.prompt-results");
        const existingEl = resultEl.querySelector('div[data-workspace-name="' + workspaceName + '"]');
        let wrapperEl;
        if (existingEl) {
            wrapperEl = existingEl;
        }
        else {
            wrapperEl = this.wrapSuggestion(el, resultEl);
        }
        let isMobile;
        try {
            isMobile = this.workspacePlugin.workspaces[workspaceName].left.type == "mobile-drawer";
        }
        catch (_a) { }
        this.addDeleteButton(wrapperEl, workspaceName);
        this.addRenameButton(wrapperEl, el);
        this.addPlatformButton(wrapperEl, isMobile ? "mobile" : "desktop");
        this.addDescription(wrapperEl, workspaceName);
    }
    wrapSuggestion(childEl, parentEl) {
        const wrapperEl = document.createElement("div");
        wrapperEl.addClass("workspace-results");
        childEl.dataset.workspaceName = childEl.textContent;
        childEl.removeClass("suggestion-item");
        childEl.addClass("workspace-item");
        childEl.addClass("workspace-name");
        if (childEl.textContent === this.workspacePlugin.activeWorkspace) {
            const activeIcon = wrapperEl.createDiv("active-workspace");
            activeIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16"><path fill="none" d="M0 0h24v24H0z"/><path d="M10 15.172l9.192-9.193 1.415 1.414L10 18l-6.364-6.364 1.414-1.414z"/></svg>`;
        }
        wrapperEl.appendChild(childEl);
        parentEl.appendChild(wrapperEl);
        // wrapperEl.appendChild(descEl);
        return wrapperEl;
    }
    addRenameButton(wrapperEl, el) {
        const renameIcon = wrapperEl.createDiv("rename-workspace");
        renameIcon.setAttribute("aria-label", "Rename workspace");
        renameIcon.setAttribute("aria-label-position", "top");
        renameIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16"><path fill="none" d="M0 0h24v24H0z"/><path d="M12.9 6.858l4.242 4.243L7.242 21H3v-4.243l9.9-9.9zm1.414-1.414l2.121-2.122a1 1 0 0 1 1.414 0l2.829 2.829a1 1 0 0 1 0 1.414l-2.122 2.121-4.242-4.242z"/></svg>`;
        renameIcon.addEventListener("click", event => this.onRenameClick(event, el));
    }
    addDeleteButton(wrapperEl, workspaceName) {
        const deleteIcon = wrapperEl.createDiv("delete-workspace");
        deleteIcon.setAttribute("aria-label", "Delete workspace");
        deleteIcon.setAttribute("aria-label-position", "top");
        deleteIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16"><path fill="none" d="M0 0h24v24H0z"/><path d="M7 4V2h10v2h5v2h-2v15a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V6H2V4h5zM6 6v14h12V6H6zm3 3h2v8H9V9zm4 0h2v8h-2V9z"/></svg>`;
        deleteIcon.addEventListener("click", event => this.deleteWorkspace(workspaceName));
    }
    addDescription(wrapperEl, workspaceName) {
        let description;
        try {
            description = this.workspacePlugin.workspaces[workspaceName][SETTINGS_ATTR$1]["description"];
        }
        catch (_a) { }
        if (description) {
            const descEl = wrapperEl.createDiv("workspace-description");
            descEl.textContent = description;
        }
    }
    addPlatformButton(wrapperEl, platform) {
        const renameIcon = wrapperEl.createDiv("platform");
        if (platform == "mobile") {
            renameIcon.setAttribute("aria-label", "Mobile workspace");
            renameIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" style="vertical-align: -0.125em;" width="16" height="16" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"><rect x="0" y="0" width="24" height="24" fill="none" stroke="none" /><path d="M3 4h17a2 2 0 0 1 2 2v2h-4V6H5v12h9v2H3a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2m14 6h6a1 1 0 0 1 1 1v10a1 1 0 0 1-1 1h-6a1 1 0 0 1-1-1V11a1 1 0 0 1 1-1m1 2v7h4v-7h-4z" fill="currentColor"/></svg>`;
        }
        else {
            renameIcon.setAttribute("aria-label", "Desktop workspace");
            renameIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" aria-hidden="true" role="img" style="vertical-align: -0.125em;" width="16" height="16" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"><rect x="0" y="0" width="24" height="24" fill="none" stroke="none" /><path d="M21 16H3V4h18m0-2H3c-1.11 0-2 .89-2 2v12a2 2 0 0 0 2 2h7v2H8v2h8v-2h-2v-2h7a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2z" fill="currentColor"/></svg>`;
        }
        renameIcon.setAttribute("aria-label-position", "top");
    }
    doDelete(workspaceName) {
        let currentSelection = this.chooser.selectedItem;
        this.workspacePlugin.deleteWorkspace(workspaceName);
        this.chooser.chooser.updateSuggestions();
        this.chooser.setSelectedItem(currentSelection - 1, true);
        this.plugin.onWorkspaceDelete(workspaceName);
    }
    getItems() {
        return [
            ...Object.keys(this.workspacePlugin.workspaces)
                .filter(workspace => !/^mode:/i.test(workspace))
                .sort(),
        ];
    }
    getItemText(item) {
        return item;
    }
    onChooseItem(item, evt) {
        let modifiers;
        if (evt.shiftKey && !evt.altKey)
            modifiers = "Shift";
        else if (evt.altKey && !evt.shiftKey)
            modifiers = "Alt";
        else
            modifiers = "";
        if (modifiers === "Shift")
            this.saveAndStay(), this.setWorkspace(item), this.close();
        else if (modifiers === "Alt")
            this.saveAndSwitch(), this.loadWorkspace(item);
        else
            this.loadWorkspace(item);
    }
    setWorkspace(workspaceName) {
        this.workspacePlugin.setActiveWorkspace(workspaceName);
    }
    loadWorkspace(workspaceName) {
        this.workspacePlugin.loadWorkspace(workspaceName);
    }
}

const SETTINGS_ATTR = "workspaces-plus:settings-v1";
class WorkspacesPlusPluginModeModal extends obsidian.FuzzySuggestModal {
    constructor(plugin, settings, hotkey = false) {
        super(plugin.app);
        this.showInstructions = false;
        this.emptyStateText = "No match found";
        this.onSuggestionClick = function (evt, itemEl) {
            if (itemEl.contentEditable === "true") {
                // allow cursor selection in rename mode by ignoring the click
                evt.stopPropagation();
                return;
            }
            evt.preventDefault();
            let item = this.chooser.suggestions.indexOf(itemEl);
            this.chooser.setSelectedItem(item), this.useSelectedItem(evt);
        };
        this.onSuggestionMouseover = function (evt, itemEl) {
            let item = this.chooser.suggestions.indexOf(itemEl);
            this.chooser.setSelectedItem(item);
        };
        this.useSelectedItem = function (evt) {
            const targetEl = evt.composedPath()[0];
            if (targetEl.contentEditable === "true") {
                this.handleRename(targetEl);
                return;
            }
            let workspaceName = this.inputEl.value ? this.inputEl.value : this.chooser.values[this.chooser.selectedItem].item;
            if (!this.values && workspaceName && evt.shiftKey) {
                this.saveAndStay();
                this.close();
                return false;
            }
            else if (!this.chooser.values)
                return false;
            let item = this.chooser.values ? this.chooser.values[this.chooser.selectedItem] : workspaceName;
            return void 0 !== item && (this.selectSuggestion(item, evt), true);
        };
        this.onRenameClick = function (evt, el) {
            evt.stopPropagation();
            if (!el)
                el = this.chooser.suggestions[this.chooser.selectedItem];
            el.parentElement.parentElement.addClass("renaming");
            if (el.contentEditable === "true") {
                el.textContent = el.dataset.workspaceName;
                el.contentEditable = "false";
                return;
            }
            else {
                el.contentEditable = "true";
            }
            const selection = window.getSelection();
            const range = document.createRange();
            selection.removeAllRanges();
            range.selectNodeContents(el);
            range.collapse(false);
            selection.addRange(range);
            el.focus();
            el.onblur = ev => {
                el.parentElement.parentElement.removeClass("renaming");
                el.contentEditable = "false";
            };
        };
        this.app = plugin.app;
        this.plugin = plugin;
        // standard initialization
        this.settings = settings;
        this.invokedViaHotkey = hotkey;
        this.workspacePlugin = this.app.internalPlugins.getPluginById("workspaces").instance;
        this.setPlaceholder("Type mode name...");
        this.buildInstructions();
        // temporary styling to force a transparent modal background to address certain themes
        // that apply a background to the modal container instead of the modal-bg
        this.bgEl.parentElement.setAttribute("style", "background-color: transparent !important");
        this.modalEl.classList.add("workspaces-plus-mode-modal");
        // handle custom modal positioning when invoked via the status bar
        if (!this.invokedViaHotkey) {
            this.bgEl.setAttribute("style", "background-color: transparent");
            this.modalEl.classList.add("quick-switch");
        }
        // setup key bindings
        this.scope = new obsidian.Scope();
        this.setupScope.apply(this);
        // setup event listeners
        this.modalEl.on("input", ".prompt-input", this.onInputChanged.bind(this));
        this.modalEl.on("click", ".workspace-item", this.onSuggestionClick.bind(this));
        this.modalEl.on("mousemove", ".workspace-item", this.onSuggestionMouseover.bind(this));
        // clone the input element as a hacky way to get rid of the obsidian onInput handler
        const inputElClone = this.inputEl.cloneNode();
        // this.modalEl.replaceChild(inputElClone, this.inputEl);
        this.inputEl = inputElClone;
    }
    onNoSuggestion() {
        this.chooser.setSuggestions(null);
        this.chooser.addMessage(this.emptyStateText);
        const el = this.chooser.containerEl.querySelector(".suggestion-empty");
        el.createEl("button", {
            cls: "list-item-part",
            text: "Save as new mode",
        }).addEventListener("click", this.saveAndStay.bind(this));
    }
    setupScope() {
        this.scope.register([], "Escape", evt => this.onEscape(evt));
        this.scope.register([], "Enter", evt => this.useSelectedItem(evt));
        this.scope.register(["Shift"], "Delete", this.deleteWorkspace.bind(this));
        this.scope.register(["Ctrl"], "Enter", evt => this.onRenameClick(evt, null));
        this.scope.register(["Shift"], "Enter", evt => this.useSelectedItem(evt));
        this.scope.register(["Alt"], "Enter", evt => this.useSelectedItem(evt));
        this.scope.register([], "ArrowUp", evt => {
            if (!evt.isComposing)
                return this.chooser.setSelectedItem(this.chooser.selectedItem - 1, true), false;
        });
        this.scope.register([], "ArrowDown", evt => {
            if (!evt.isComposing)
                return this.chooser.setSelectedItem(this.chooser.selectedItem + 1, true), false;
        });
    }
    buildInstructions() {
        if (this.settings.showInstructions || this.invokedViaHotkey) {
            let instructions;
            instructions = [
                {
                    command: "↵",
                    purpose: "load",
                },
                {
                    command: "ctrl ↵",
                    purpose: "rename",
                },
                {
                    command: "shift ⌫",
                    purpose: "delete",
                },
                {
                    command: "esc",
                    purpose: "cancel",
                },
            ];
            this.setInstructions(instructions);
        }
    }
    onInputChanged() {
        this.chooser.chooser.updateSuggestions();
    }
    onEscape(evt) {
        const evtTargetEl = evt.target;
        // if we're actively renaming a workspace, escape out of the rename
        if (evtTargetEl.classList.contains("workspace-item") && evtTargetEl.contentEditable === "true") {
            evtTargetEl.textContent = evtTargetEl.dataset.workspaceName;
            evtTargetEl.contentEditable = "false";
            return;
        }
        // otherwise, close the modal
        this.close();
    }
    open() {
        this.app.keymap.pushScope(this.scope);
        document.body.appendChild(this.containerEl);
        if (!this.invokedViaHotkey) {
            this.popper = createPopper(document.body.querySelector(".plugin-workspaces-plus.mode-switcher"), this.modalEl, {
                placement: "top-start",
                modifiers: [{ name: "offset", options: { offset: [0, 10] } }],
            });
        }
        this.onOpen();
        this.app.workspace.pushClosable(this);
    }
    onOpen() {
        var _a;
        super.onOpen();
        this.activeWorkspace = this.workspacePlugin.activeWorkspace;
        let selectedIdx = this.getItems().findIndex(workspace => workspace === this.activeWorkspace);
        this.chooser.setSelectedItem(selectedIdx);
        (_a = this.chooser.suggestions[this.chooser.selectedItem]) === null || _a === void 0 ? void 0 : _a.scrollIntoViewIfNeeded();
    }
    onClose() {
        this.app.keymap.popScope(this.scope);
        super.onClose();
    }
    handleRename(targetEl) {
        // TODO: Update all workspaces on mode rename
        targetEl.parentElement.parentElement.removeClass("renaming");
        const originalName = "Mode: " + targetEl.dataset.workspaceName;
        const newName = "Mode: " + targetEl.textContent;
        // let settings = this.workspacePlugin.workspaces[this.workspacePlugin.activeWorkspace][SETTINGS_ATTR];
        // let currentMode = settings["mode"] ? settings["mode"] : null;
        for (const [workspaceName, workspace] of Object.entries(this.workspacePlugin.workspaces)) {
            let settings = workspace[SETTINGS_ATTR];
            let mode = settings ? settings["mode"] : null;
            if (mode && mode == originalName) {
                workspace[SETTINGS_ATTR]["mode"] = newName;
            }
        }
        this.workspacePlugin.workspaces[newName] = this.workspacePlugin.workspaces[originalName];
        delete this.workspacePlugin.workspaces[originalName];
        if (originalName === this.activeWorkspace) {
            this.setWorkspace(newName);
            this.activeWorkspace = newName;
        }
        this.chooser.chooser.updateSuggestions();
        targetEl.contentEditable = "false";
        let selectedIdx = this.getItems().findIndex((workspace) => workspace === newName);
        this.chooser.setSelectedItem(selectedIdx, true);
        this.app.workspace.trigger("workspace-rename", newName, originalName);
    }
    saveAndStay() {
        let workspaceName = this.inputEl.value ? this.inputEl.value : this.chooser.values[this.chooser.selectedItem].item;
        this.workspacePlugin.saveWorkspace("Mode: " + workspaceName);
        this.chooser.chooser.updateSuggestions();
        new obsidian.Notice("Successfully saved mode: " + workspaceName);
    }
    deleteWorkspace(workspaceName = null) {
        if (!workspaceName) {
            let currentSelection = this.chooser.selectedItem;
            workspaceName = this.chooser.values[currentSelection].item;
        }
        if (this.settings.showDeletePrompt) {
            createConfirmationDialog(this.app, {
                cta: "Delete",
                onAccept: () => __awaiter(this, void 0, void 0, function* () {
                    this.doDelete("Mode: " + workspaceName);
                }),
                text: `Do you really want to delete the '` + workspaceName + `' mode?`,
                title: "Mode Delete Confirmation",
            });
        }
        else {
            this.doDelete("Mode: " + workspaceName);
        }
    }
    renderSuggestion(item, el) {
        super.renderSuggestion(item, el);
        const resultEl = document.body.querySelector("div.workspaces-plus-mode-modal div.prompt-results");
        const existingEl = resultEl.querySelector('div[data-workspace-name="' + el.textContent + '"]');
        let wrapperEl;
        if (existingEl) {
            wrapperEl = existingEl;
        }
        else {
            wrapperEl = this.wrapSuggestion(el, resultEl);
        }
        this.addDeleteButton(wrapperEl);
        this.addRenameButton(wrapperEl, el);
    }
    wrapSuggestion(childEl, parentEl) {
        const wrapperEl = document.createElement("div");
        wrapperEl.addClass("workspace-results");
        childEl.dataset.workspaceName = childEl.textContent;
        childEl.removeClass("suggestion-item");
        childEl.addClass("workspace-item");
        let mode;
        try {
            mode = this.workspacePlugin.workspaces[this.workspacePlugin.activeWorkspace][SETTINGS_ATTR]["mode"].replace(/^mode: /i, "");
        }
        catch (_a) { }
        if (childEl.textContent === mode) {
            const activeIcon = wrapperEl.createDiv("active-workspace");
            activeIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16"><path fill="none" d="M0 0h24v24H0z"/><path d="M10 15.172l9.192-9.193 1.415 1.414L10 18l-6.364-6.364 1.414-1.414z"/></svg>`;
        }
        wrapperEl.appendChild(childEl);
        parentEl.appendChild(wrapperEl);
        return wrapperEl;
    }
    addRenameButton(wrapperEl, el) {
        const renameIcon = wrapperEl.createDiv("rename-workspace");
        renameIcon.setAttribute("aria-label", "Rename mode");
        renameIcon.setAttribute("aria-label-position", "top");
        renameIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16"><path fill="none" d="M0 0h24v24H0z"/><path d="M12.9 6.858l4.242 4.243L7.242 21H3v-4.243l9.9-9.9zm1.414-1.414l2.121-2.122a1 1 0 0 1 1.414 0l2.829 2.829a1 1 0 0 1 0 1.414l-2.122 2.121-4.242-4.242z"/></svg>`;
        renameIcon.addEventListener("click", event => this.onRenameClick(event, el));
    }
    addDeleteButton(wrapperEl) {
        const deleteIcon = wrapperEl.createDiv("delete-workspace");
        deleteIcon.setAttribute("aria-label", "Delete mode");
        deleteIcon.setAttribute("aria-label-position", "top");
        deleteIcon.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16"><path fill="none" d="M0 0h24v24H0z"/><path d="M7 4V2h10v2h5v2h-2v15a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V6H2V4h5zM6 6v14h12V6H6zm3 3h2v8H9V9zm4 0h2v8h-2V9z"/></svg>`;
        deleteIcon.addEventListener("click", event => this.deleteWorkspace());
    }
    doDelete(workspaceName) {
        let currentSelection = this.chooser.selectedItem;
        this.workspacePlugin.deleteWorkspace(workspaceName);
        this.chooser.chooser.updateSuggestions();
        this.chooser.setSelectedItem(currentSelection - 1, true);
        this.plugin.onWorkspaceDelete(workspaceName);
        for (const [workspaceName, workspace] of Object.entries(this.workspacePlugin.workspaces)) {
            let settings = workspace[SETTINGS_ATTR];
            let mode = settings ? settings["mode"] : null;
            if (mode && mode == workspaceName) {
                workspace[SETTINGS_ATTR]["mode"] = null;
            }
        }
    }
    getItems() {
        return [
            ...Object.keys(this.workspacePlugin.workspaces)
                .filter(workspace => /^mode:/i.test(workspace))
                .map(workspace => workspace.replace(/mode: /i, ""))
                .sort(),
        ];
    }
    getItemText(item) {
        return item;
    }
    onChooseItem(item, evt) {
        let modifiers;
        if (evt.shiftKey && !evt.altKey)
            modifiers = "Shift";
        else if (evt.altKey && !evt.shiftKey)
            modifiers = "Alt";
        else
            modifiers = "";
        if (modifiers === "Shift")
            this.saveAndStay(), this.close();
        else
            this.loadWorkspace("Mode: " + item);
    }
    setWorkspace(workspaceName) {
        this.workspacePlugin.setActiveWorkspace(workspaceName);
    }
    loadWorkspace(workspaceName) {
        this.workspacePlugin.loadWorkspace(workspaceName);
    }
}

function around(obj, factories) {
    const removers = Object.keys(factories).map(key => around1(obj, key, factories[key]));
    return removers.length === 1 ? removers[0] : function () { removers.forEach(r => r()); };
}
function around1(obj, method, createWrapper) {
    const original = obj[method], hadOwn = obj.hasOwnProperty(method);
    let current = createWrapper(original);
    // Let our wrapper inherit static props from the wrapping method,
    // and the wrapping method, props from the original method
    if (original)
        Object.setPrototypeOf(current, original);
    Object.setPrototypeOf(wrapper, current);
    obj[method] = wrapper;
    // Return a callback to allow safe removal
    return remove;
    function wrapper(...args) {
        // If we have been deactivated and are no longer wrapped, remove ourselves
        if (current === original && obj[method] === wrapper)
            remove();
        return current.apply(this, args);
    }
    function remove() {
        // If no other patches, just do a direct removal
        if (obj[method] === wrapper) {
            if (hadOwn)
                obj[method] = original;
            else
                delete obj[method];
        }
        if (current === original)
            return;
        // Else pass future calls through, and remove wrapper from the prototype chain
        current = original;
        Object.setPrototypeOf(wrapper, original || Function);
    }
}

const DEFAULT_DAILY_NOTE_FORMAT = "YYYY-MM-DD";
const DEFAULT_WEEKLY_NOTE_FORMAT = "gggg-[W]ww";
const DEFAULT_MONTHLY_NOTE_FORMAT = "YYYY-MM";
const DEFAULT_QUARTERLY_NOTE_FORMAT = "YYYY-[Q]Q";
const DEFAULT_YEARLY_NOTE_FORMAT = "YYYY";

function shouldUsePeriodicNotesSettings(periodicity) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const periodicNotes = window.app.plugins.getPlugin("periodic-notes");
    return periodicNotes && periodicNotes.settings?.[periodicity]?.enabled;
}
/**
 * Read the user settings for the `daily-notes` plugin
 * to keep behavior of creating a new note in-sync.
 */
function getDailyNoteSettings() {
    try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const { internalPlugins, plugins } = window.app;
        if (shouldUsePeriodicNotesSettings("daily")) {
            const { format, folder, template } = plugins.getPlugin("periodic-notes")?.settings?.daily || {};
            return {
                format: format || DEFAULT_DAILY_NOTE_FORMAT,
                folder: folder?.trim() || "",
                template: template?.trim() || "",
            };
        }
        const { folder, format, template } = internalPlugins.getPluginById("daily-notes")?.instance?.options || {};
        return {
            format: format || DEFAULT_DAILY_NOTE_FORMAT,
            folder: folder?.trim() || "",
            template: template?.trim() || "",
        };
    }
    catch (err) {
        console.info("No custom daily note settings found!", err);
    }
}
/**
 * Read the user settings for the `weekly-notes` plugin
 * to keep behavior of creating a new note in-sync.
 */
function getWeeklyNoteSettings() {
    try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const pluginManager = window.app.plugins;
        const calendarSettings = pluginManager.getPlugin("calendar")?.options;
        const periodicNotesSettings = pluginManager.getPlugin("periodic-notes")?.settings?.weekly;
        if (shouldUsePeriodicNotesSettings("weekly")) {
            return {
                format: periodicNotesSettings.format || DEFAULT_WEEKLY_NOTE_FORMAT,
                folder: periodicNotesSettings.folder?.trim() || "",
                template: periodicNotesSettings.template?.trim() || "",
            };
        }
        const settings = calendarSettings || {};
        return {
            format: settings.weeklyNoteFormat || DEFAULT_WEEKLY_NOTE_FORMAT,
            folder: settings.weeklyNoteFolder?.trim() || "",
            template: settings.weeklyNoteTemplate?.trim() || "",
        };
    }
    catch (err) {
        console.info("No custom weekly note settings found!", err);
    }
}
/**
 * Read the user settings for the `periodic-notes` plugin
 * to keep behavior of creating a new note in-sync.
 */
function getMonthlyNoteSettings() {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pluginManager = window.app.plugins;
    try {
        const settings = (shouldUsePeriodicNotesSettings("monthly") &&
            pluginManager.getPlugin("periodic-notes")?.settings?.monthly) ||
            {};
        return {
            format: settings.format || DEFAULT_MONTHLY_NOTE_FORMAT,
            folder: settings.folder?.trim() || "",
            template: settings.template?.trim() || "",
        };
    }
    catch (err) {
        console.info("No custom monthly note settings found!", err);
    }
}
/**
 * Read the user settings for the `periodic-notes` plugin
 * to keep behavior of creating a new note in-sync.
 */
function getQuarterlyNoteSettings() {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pluginManager = window.app.plugins;
    try {
        const settings = (shouldUsePeriodicNotesSettings("quarterly") &&
            pluginManager.getPlugin("periodic-notes")?.settings?.quarterly) ||
            {};
        return {
            format: settings.format || DEFAULT_QUARTERLY_NOTE_FORMAT,
            folder: settings.folder?.trim() || "",
            template: settings.template?.trim() || "",
        };
    }
    catch (err) {
        console.info("No custom quarterly note settings found!", err);
    }
}
/**
 * Read the user settings for the `periodic-notes` plugin
 * to keep behavior of creating a new note in-sync.
 */
function getYearlyNoteSettings() {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pluginManager = window.app.plugins;
    try {
        const settings = (shouldUsePeriodicNotesSettings("yearly") &&
            pluginManager.getPlugin("periodic-notes")?.settings?.yearly) ||
            {};
        return {
            format: settings.format || DEFAULT_YEARLY_NOTE_FORMAT,
            folder: settings.folder?.trim() || "",
            template: settings.template?.trim() || "",
        };
    }
    catch (err) {
        console.info("No custom yearly note settings found!", err);
    }
}

// Credit: @creationix/path.js
function join(...partSegments) {
    // Split the inputs into a list of path commands.
    let parts = [];
    for (let i = 0, l = partSegments.length; i < l; i++) {
        parts = parts.concat(partSegments[i].split("/"));
    }
    // Interpret the path commands to get the new resolved path.
    const newParts = [];
    for (let i = 0, l = parts.length; i < l; i++) {
        const part = parts[i];
        // Remove leading and trailing slashes
        // Also remove "." segments
        if (!part || part === ".")
            continue;
        // Push new path segments.
        else
            newParts.push(part);
    }
    // Preserve the initial slash if there was one.
    if (parts[0] === "")
        newParts.unshift("");
    // Turn back into a single string path.
    return newParts.join("/");
}
function basename(fullPath) {
    let base = fullPath.substring(fullPath.lastIndexOf("/") + 1);
    if (base.lastIndexOf(".") != -1)
        base = base.substring(0, base.lastIndexOf("."));
    return base;
}
async function ensureFolderExists(path) {
    const dirs = path.replace(/\\/g, "/").split("/");
    dirs.pop(); // remove basename
    if (dirs.length) {
        const dir = join(...dirs);
        if (!window.app.vault.getAbstractFileByPath(dir)) {
            await window.app.vault.createFolder(dir);
        }
    }
}
async function getNotePath(directory, filename) {
    if (!filename.endsWith(".md")) {
        filename += ".md";
    }
    const path = obsidian__default["default"].normalizePath(join(directory, filename));
    await ensureFolderExists(path);
    return path;
}
async function getTemplateInfo(template) {
    const { metadataCache, vault } = window.app;
    const templatePath = obsidian__default["default"].normalizePath(template);
    if (templatePath === "/") {
        return Promise.resolve(["", null]);
    }
    try {
        const templateFile = metadataCache.getFirstLinkpathDest(templatePath, "");
        const contents = await vault.cachedRead(templateFile);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const IFoldInfo = window.app.foldManager.load(templateFile);
        return [contents, IFoldInfo];
    }
    catch (err) {
        console.error(`Failed to read the daily note template '${templatePath}'`, err);
        new obsidian__default["default"].Notice("Failed to read the daily note template");
        return ["", null];
    }
}

/**
 * dateUID is a way of weekly identifying daily/weekly/monthly notes.
 * They are prefixed with the granularity to avoid ambiguity.
 */
function getDateUID(date, granularity = "day") {
    const ts = date.clone().startOf(granularity).format();
    return `${granularity}-${ts}`;
}
function removeEscapedCharacters(format) {
    return format.replace(/\[[^\]]*\]/g, ""); // remove everything within brackets
}
/**
 * XXX: When parsing dates that contain both week numbers and months,
 * Moment choses to ignore the week numbers. For the week dateUID, we
 * want the opposite behavior. Strip the MMM from the format to patch.
 */
function isFormatAmbiguous(format, granularity) {
    if (granularity === "week") {
        const cleanFormat = removeEscapedCharacters(format);
        return (/w{1,2}/i.test(cleanFormat) &&
            (/M{1,4}/.test(cleanFormat) || /D{1,4}/.test(cleanFormat)));
    }
    return false;
}
function getDateFromFile(file, granularity) {
    return getDateFromFilename(file.basename, granularity);
}
function getDateFromPath(path, granularity) {
    return getDateFromFilename(basename(path), granularity);
}
function getDateFromFilename(filename, granularity) {
    const getSettings = {
        day: getDailyNoteSettings,
        week: getWeeklyNoteSettings,
        month: getMonthlyNoteSettings,
        quarter: getQuarterlyNoteSettings,
        year: getYearlyNoteSettings,
    };
    const format = getSettings[granularity]().format.split("/").pop();
    const noteDate = window.moment(filename, format, true);
    if (!noteDate.isValid()) {
        return null;
    }
    if (isFormatAmbiguous(format, granularity)) {
        if (granularity === "week") {
            const cleanFormat = removeEscapedCharacters(format);
            if (/w{1,2}/i.test(cleanFormat)) {
                return window.moment(filename, 
                // If format contains week, remove day & month formatting
                format.replace(/M{1,4}/g, "").replace(/D{1,4}/g, ""), false);
            }
        }
    }
    return noteDate;
}

class DailyNotesFolderMissingError extends Error {
}
/**
 * This function mimics the behavior of the daily-notes plugin
 * so it will replace {{date}}, {{title}}, and {{time}} with the
 * formatted timestamp.
 *
 * Note: it has an added bonus that it's not 'today' specific.
 */
async function createDailyNote(date) {
    const app = window.app;
    const { vault } = app;
    const moment = window.moment;
    const { template, format, folder } = getDailyNoteSettings();
    const [templateContents, IFoldInfo] = await getTemplateInfo(template);
    const filename = date.format(format);
    const normalizedPath = await getNotePath(folder, filename);
    try {
        const createdFile = await vault.create(normalizedPath, templateContents
            .replace(/{{\s*date\s*}}/gi, filename)
            .replace(/{{\s*time\s*}}/gi, moment().format("HH:mm"))
            .replace(/{{\s*title\s*}}/gi, filename)
            .replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi, (_, _timeOrDate, calc, timeDelta, unit, momentFormat) => {
            const now = moment();
            const currentDate = date.clone().set({
                hour: now.get("hour"),
                minute: now.get("minute"),
                second: now.get("second"),
            });
            if (calc) {
                currentDate.add(parseInt(timeDelta, 10), unit);
            }
            if (momentFormat) {
                return currentDate.format(momentFormat.substring(1).trim());
            }
            return currentDate.format(format);
        })
            .replace(/{{\s*yesterday\s*}}/gi, date.clone().subtract(1, "day").format(format))
            .replace(/{{\s*tomorrow\s*}}/gi, date.clone().add(1, "d").format(format)));
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        app.foldManager.save(createdFile, IFoldInfo);
        return createdFile;
    }
    catch (err) {
        console.error(`Failed to create file: '${normalizedPath}'`, err);
        new obsidian__default["default"].Notice("Unable to create new file.");
    }
}
function getDailyNote(date, dailyNotes) {
    return dailyNotes[getDateUID(date, "day")] ?? null;
}
function getAllDailyNotes() {
    /**
     * Find all daily notes in the daily note folder
     */
    const { vault } = window.app;
    const { folder } = getDailyNoteSettings();
    const dailyNotesFolder = vault.getAbstractFileByPath(obsidian__default["default"].normalizePath(folder));
    if (!dailyNotesFolder) {
        throw new DailyNotesFolderMissingError("Failed to find daily notes folder");
    }
    const dailyNotes = {};
    obsidian__default["default"].Vault.recurseChildren(dailyNotesFolder, (note) => {
        if (note instanceof obsidian__default["default"].TFile) {
            const date = getDateFromFile(note, "day");
            if (date) {
                const dateString = getDateUID(date, "day");
                dailyNotes[dateString] = note;
            }
        }
    });
    return dailyNotes;
}

class WeeklyNotesFolderMissingError extends Error {
}
function getDaysOfWeek() {
    const { moment } = window;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let weekStart = moment.localeData()._week.dow;
    const daysOfWeek = [
        "sunday",
        "monday",
        "tuesday",
        "wednesday",
        "thursday",
        "friday",
        "saturday",
    ];
    while (weekStart) {
        daysOfWeek.push(daysOfWeek.shift());
        weekStart--;
    }
    return daysOfWeek;
}
function getDayOfWeekNumericalValue(dayOfWeekName) {
    return getDaysOfWeek().indexOf(dayOfWeekName.toLowerCase());
}
async function createWeeklyNote(date) {
    const { vault } = window.app;
    const { template, format, folder } = getWeeklyNoteSettings();
    const [templateContents, IFoldInfo] = await getTemplateInfo(template);
    const filename = date.format(format);
    const normalizedPath = await getNotePath(folder, filename);
    try {
        const createdFile = await vault.create(normalizedPath, templateContents
            .replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi, (_, _timeOrDate, calc, timeDelta, unit, momentFormat) => {
            const now = window.moment();
            const currentDate = date.clone().set({
                hour: now.get("hour"),
                minute: now.get("minute"),
                second: now.get("second"),
            });
            if (calc) {
                currentDate.add(parseInt(timeDelta, 10), unit);
            }
            if (momentFormat) {
                return currentDate.format(momentFormat.substring(1).trim());
            }
            return currentDate.format(format);
        })
            .replace(/{{\s*title\s*}}/gi, filename)
            .replace(/{{\s*time\s*}}/gi, window.moment().format("HH:mm"))
            .replace(/{{\s*(sunday|monday|tuesday|wednesday|thursday|friday|saturday)\s*:(.*?)}}/gi, (_, dayOfWeek, momentFormat) => {
            const day = getDayOfWeekNumericalValue(dayOfWeek);
            return date.weekday(day).format(momentFormat.trim());
        }));
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        window.app.foldManager.save(createdFile, IFoldInfo);
        return createdFile;
    }
    catch (err) {
        console.error(`Failed to create file: '${normalizedPath}'`, err);
        new obsidian__default["default"].Notice("Unable to create new file.");
    }
}
function getWeeklyNote(date, weeklyNotes) {
    return weeklyNotes[getDateUID(date, "week")] ?? null;
}
function getAllWeeklyNotes() {
    const weeklyNotes = {};
    if (!appHasWeeklyNotesPluginLoaded()) {
        return weeklyNotes;
    }
    const { vault } = window.app;
    const { folder } = getWeeklyNoteSettings();
    const weeklyNotesFolder = vault.getAbstractFileByPath(obsidian__default["default"].normalizePath(folder));
    if (!weeklyNotesFolder) {
        throw new WeeklyNotesFolderMissingError("Failed to find weekly notes folder");
    }
    obsidian__default["default"].Vault.recurseChildren(weeklyNotesFolder, (note) => {
        if (note instanceof obsidian__default["default"].TFile) {
            const date = getDateFromFile(note, "week");
            if (date) {
                const dateString = getDateUID(date, "week");
                weeklyNotes[dateString] = note;
            }
        }
    });
    return weeklyNotes;
}

class MonthlyNotesFolderMissingError extends Error {
}
/**
 * This function mimics the behavior of the daily-notes plugin
 * so it will replace {{date}}, {{title}}, and {{time}} with the
 * formatted timestamp.
 *
 * Note: it has an added bonus that it's not 'today' specific.
 */
async function createMonthlyNote(date) {
    const { vault } = window.app;
    const { template, format, folder } = getMonthlyNoteSettings();
    const [templateContents, IFoldInfo] = await getTemplateInfo(template);
    const filename = date.format(format);
    const normalizedPath = await getNotePath(folder, filename);
    try {
        const createdFile = await vault.create(normalizedPath, templateContents
            .replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi, (_, _timeOrDate, calc, timeDelta, unit, momentFormat) => {
            const now = window.moment();
            const currentDate = date.clone().set({
                hour: now.get("hour"),
                minute: now.get("minute"),
                second: now.get("second"),
            });
            if (calc) {
                currentDate.add(parseInt(timeDelta, 10), unit);
            }
            if (momentFormat) {
                return currentDate.format(momentFormat.substring(1).trim());
            }
            return currentDate.format(format);
        })
            .replace(/{{\s*date\s*}}/gi, filename)
            .replace(/{{\s*time\s*}}/gi, window.moment().format("HH:mm"))
            .replace(/{{\s*title\s*}}/gi, filename));
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        window.app.foldManager.save(createdFile, IFoldInfo);
        return createdFile;
    }
    catch (err) {
        console.error(`Failed to create file: '${normalizedPath}'`, err);
        new obsidian__default["default"].Notice("Unable to create new file.");
    }
}
function getMonthlyNote(date, monthlyNotes) {
    return monthlyNotes[getDateUID(date, "month")] ?? null;
}
function getAllMonthlyNotes() {
    const monthlyNotes = {};
    if (!appHasMonthlyNotesPluginLoaded()) {
        return monthlyNotes;
    }
    const { vault } = window.app;
    const { folder } = getMonthlyNoteSettings();
    const monthlyNotesFolder = vault.getAbstractFileByPath(obsidian__default["default"].normalizePath(folder));
    if (!monthlyNotesFolder) {
        throw new MonthlyNotesFolderMissingError("Failed to find monthly notes folder");
    }
    obsidian__default["default"].Vault.recurseChildren(monthlyNotesFolder, (note) => {
        if (note instanceof obsidian__default["default"].TFile) {
            const date = getDateFromFile(note, "month");
            if (date) {
                const dateString = getDateUID(date, "month");
                monthlyNotes[dateString] = note;
            }
        }
    });
    return monthlyNotes;
}

class QuarterlyNotesFolderMissingError extends Error {
}
/**
 * This function mimics the behavior of the daily-notes plugin
 * so it will replace {{date}}, {{title}}, and {{time}} with the
 * formatted timestamp.
 *
 * Note: it has an added bonus that it's not 'today' specific.
 */
async function createQuarterlyNote(date) {
    const { vault } = window.app;
    const { template, format, folder } = getQuarterlyNoteSettings();
    const [templateContents, IFoldInfo] = await getTemplateInfo(template);
    const filename = date.format(format);
    const normalizedPath = await getNotePath(folder, filename);
    try {
        const createdFile = await vault.create(normalizedPath, templateContents
            .replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi, (_, _timeOrDate, calc, timeDelta, unit, momentFormat) => {
            const now = window.moment();
            const currentDate = date.clone().set({
                hour: now.get("hour"),
                minute: now.get("minute"),
                second: now.get("second"),
            });
            if (calc) {
                currentDate.add(parseInt(timeDelta, 10), unit);
            }
            if (momentFormat) {
                return currentDate.format(momentFormat.substring(1).trim());
            }
            return currentDate.format(format);
        })
            .replace(/{{\s*date\s*}}/gi, filename)
            .replace(/{{\s*time\s*}}/gi, window.moment().format("HH:mm"))
            .replace(/{{\s*title\s*}}/gi, filename));
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        window.app.foldManager.save(createdFile, IFoldInfo);
        return createdFile;
    }
    catch (err) {
        console.error(`Failed to create file: '${normalizedPath}'`, err);
        new obsidian__default["default"].Notice("Unable to create new file.");
    }
}
function getQuarterlyNote(date, quarterly) {
    return quarterly[getDateUID(date, "quarter")] ?? null;
}
function getAllQuarterlyNotes() {
    const quarterly = {};
    if (!appHasQuarterlyNotesPluginLoaded()) {
        return quarterly;
    }
    const { vault } = window.app;
    const { folder } = getQuarterlyNoteSettings();
    const quarterlyFolder = vault.getAbstractFileByPath(obsidian__default["default"].normalizePath(folder));
    if (!quarterlyFolder) {
        throw new QuarterlyNotesFolderMissingError("Failed to find quarterly notes folder");
    }
    obsidian__default["default"].Vault.recurseChildren(quarterlyFolder, (note) => {
        if (note instanceof obsidian__default["default"].TFile) {
            const date = getDateFromFile(note, "quarter");
            if (date) {
                const dateString = getDateUID(date, "quarter");
                quarterly[dateString] = note;
            }
        }
    });
    return quarterly;
}

class YearlyNotesFolderMissingError extends Error {
}
/**
 * This function mimics the behavior of the daily-notes plugin
 * so it will replace {{date}}, {{title}}, and {{time}} with the
 * formatted timestamp.
 *
 * Note: it has an added bonus that it's not 'today' specific.
 */
async function createYearlyNote(date) {
    const { vault } = window.app;
    const { template, format, folder } = getYearlyNoteSettings();
    const [templateContents, IFoldInfo] = await getTemplateInfo(template);
    const filename = date.format(format);
    const normalizedPath = await getNotePath(folder, filename);
    try {
        const createdFile = await vault.create(normalizedPath, templateContents
            .replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi, (_, _timeOrDate, calc, timeDelta, unit, momentFormat) => {
            const now = window.moment();
            const currentDate = date.clone().set({
                hour: now.get("hour"),
                minute: now.get("minute"),
                second: now.get("second"),
            });
            if (calc) {
                currentDate.add(parseInt(timeDelta, 10), unit);
            }
            if (momentFormat) {
                return currentDate.format(momentFormat.substring(1).trim());
            }
            return currentDate.format(format);
        })
            .replace(/{{\s*date\s*}}/gi, filename)
            .replace(/{{\s*time\s*}}/gi, window.moment().format("HH:mm"))
            .replace(/{{\s*title\s*}}/gi, filename));
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        window.app.foldManager.save(createdFile, IFoldInfo);
        return createdFile;
    }
    catch (err) {
        console.error(`Failed to create file: '${normalizedPath}'`, err);
        new obsidian__default["default"].Notice("Unable to create new file.");
    }
}
function getYearlyNote(date, yearlyNotes) {
    return yearlyNotes[getDateUID(date, "year")] ?? null;
}
function getAllYearlyNotes() {
    const yearlyNotes = {};
    if (!appHasYearlyNotesPluginLoaded()) {
        return yearlyNotes;
    }
    const { vault } = window.app;
    const { folder } = getYearlyNoteSettings();
    const yearlyNotesFolder = vault.getAbstractFileByPath(obsidian__default["default"].normalizePath(folder));
    if (!yearlyNotesFolder) {
        throw new YearlyNotesFolderMissingError("Failed to find yearly notes folder");
    }
    obsidian__default["default"].Vault.recurseChildren(yearlyNotesFolder, (note) => {
        if (note instanceof obsidian__default["default"].TFile) {
            const date = getDateFromFile(note, "year");
            if (date) {
                const dateString = getDateUID(date, "year");
                yearlyNotes[dateString] = note;
            }
        }
    });
    return yearlyNotes;
}
/**
 * XXX: "Weekly Notes" live in either the Calendar plugin or the periodic-notes plugin.
 * Check both until the weekly notes feature is removed from the Calendar plugin.
 */
function appHasWeeklyNotesPluginLoaded() {
    const { app } = window;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    if (app.plugins.getPlugin("calendar")) {
        return true;
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const periodicNotes = app.plugins.getPlugin("periodic-notes");
    return periodicNotes && periodicNotes.settings?.weekly?.enabled;
}
function appHasMonthlyNotesPluginLoaded() {
    const { app } = window;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const periodicNotes = app.plugins.getPlugin("periodic-notes");
    return periodicNotes && periodicNotes.settings?.monthly?.enabled;
}
function appHasQuarterlyNotesPluginLoaded() {
    const { app } = window;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const periodicNotes = app.plugins.getPlugin("periodic-notes");
    return periodicNotes && periodicNotes.settings?.quarterly?.enabled;
}
function appHasYearlyNotesPluginLoaded() {
    const { app } = window;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const periodicNotes = app.plugins.getPlugin("periodic-notes");
    return periodicNotes && periodicNotes.settings?.yearly?.enabled;
}
function getPeriodicNoteSettings(granularity) {
    const getSettings = {
        day: getDailyNoteSettings,
        week: getWeeklyNoteSettings,
        month: getMonthlyNoteSettings,
        quarter: getQuarterlyNoteSettings,
        year: getYearlyNoteSettings,
    }[granularity];
    return getSettings();
}
var createDailyNote_1 = createDailyNote;
var createMonthlyNote_1 = createMonthlyNote;
var createQuarterlyNote_1 = createQuarterlyNote;
var createWeeklyNote_1 = createWeeklyNote;
var createYearlyNote_1 = createYearlyNote;
var getAllDailyNotes_1 = getAllDailyNotes;
var getAllMonthlyNotes_1 = getAllMonthlyNotes;
var getAllQuarterlyNotes_1 = getAllQuarterlyNotes;
var getAllWeeklyNotes_1 = getAllWeeklyNotes;
var getAllYearlyNotes_1 = getAllYearlyNotes;
var getDailyNote_1 = getDailyNote;
var getDateFromPath_1 = getDateFromPath;
var getMonthlyNote_1 = getMonthlyNote;
var getPeriodicNoteSettings_1 = getPeriodicNoteSettings;
var getQuarterlyNote_1 = getQuarterlyNote;
var getWeeklyNote_1 = getWeeklyNote;
var getYearlyNote_1 = getYearlyNote;

function pathJoin(parts, sep) {
    const separator = sep || "/";
    parts = parts.map((part, index) => {
        if (index) {
            part = part.replace(new RegExp("^" + separator), "");
        }
        if (index !== parts.length - 1) {
            part = part.replace(new RegExp(separator + "$"), "");
        }
        return part;
    });
    return parts.join(separator);
}
class Utils {
    constructor(plugin) {
        this.SETTINGS_ATTR = "workspaces-plus:settings-v1";
        this.plugin = plugin;
        this.app = plugin.app;
        this.workspacePlugin = this.app.internalPlugins.getPluginById("workspaces").instance;
    }
    getWorkspace(name) {
        return this.workspacePlugin.workspaces[name];
    }
    getWorkspaceSettings(name) {
        const workspace = this.getWorkspace(name);
        if (!workspace)
            return null;
        return workspace[this.SETTINGS_ATTR] ? workspace[this.SETTINGS_ATTR] : (workspace[this.SETTINGS_ATTR] = {});
    }
    get activeModeName() {
        const settings = this.activeWorkspaceSettings();
        return settings === null || settings === void 0 ? void 0 : settings.mode;
    }
    saveActiveMode() {
        this.activeModeName && this.workspacePlugin.saveWorkspace(this.activeModeName);
    }
    saveActiveWorkspace() {
        this.activeWorkspace && this.workspacePlugin.saveWorkspace(this.activeWorkspace);
    }
    getActiveModeDisplayName() {
        return this.activeModeName ? this.activeModeName.replace(/^mode: /i, "") : "Global";
    }
    setWorkspaceSettings(name, settings) {
        const workspace = this.getWorkspace(name);
        workspace[this.SETTINGS_ATTR] = settings;
        return workspace[this.SETTINGS_ATTR];
    }
    get activeWorkspace() {
        return this.workspacePlugin.activeWorkspace;
    }
    activeWorkspaceSettings() {
        return this.getWorkspaceSettings(this.activeWorkspace);
    }
    isMode(name) {
        return name.match(/^mode:/i) ? true : false;
    }
    get isNativePluginEnabled() {
        return this.workspacePlugin.plugin._loaded;
    }
    getMode(name) {
        if (this.isMode(name))
            return this.getWorkspace(name);
    }
    loadMode(workspaceName, modeName) {
        const workspace = this.getWorkspace(workspaceName);
        const workspaceSettings = this.getWorkspaceSettings(workspaceName);
        const mode = this.getMode(modeName);
        const modeSettings = this.getModeSettings(modeName);
        // logic to allow for toggling a mode off/on
        if ((workspaceSettings === null || workspaceSettings === void 0 ? void 0 : workspaceSettings.mode) === modeName) {
            workspaceSettings.mode = null;
        }
        else {
            workspaceSettings && (workspaceSettings.mode = modeName);
        }
        // load the mode's sidebar layouts, if enabled
        if ((modeSettings === null || modeSettings === void 0 ? void 0 : modeSettings.saveSidebar) && workspaceSettings.mode) {
            mode && this.mergeSidebarLayout(mode);
            this.updateFoldState(modeSettings);
        }
        else {
            workspace && this.mergeSidebarLayout(workspace);
            this.updateFoldState(workspaceSettings);
        }
        this.workspacePlugin.saveData(); // call saveData on the workspace plugin to persist the workspace metadata to disk
        return true;
    }
    setChildId(split, leafId, fileName) {
        let found = false;
        function recurse(split, leafId, fileName) {
            if (found)
                return;
            if (split.type == "leaf") {
                if (split.id == leafId) {
                    if (fileName) {
                        split.state.state.file = fileName;
                    }
                    else {
                        split.state.state.file = null;
                    }
                    found = true;
                }
            }
            else if (split.type == "split") {
                split.children.forEach((child) => {
                    recurse(child, leafId, fileName);
                });
            }
        }
        recurse(split, leafId, fileName);
        return found;
    }
    createPeriodicNote(granularity, date) {
        const createFn = {
            day: createDailyNote_1,
            week: createWeeklyNote_1,
            month: createMonthlyNote_1,
            quarter: createQuarterlyNote_1,
            year: createYearlyNote_1,
        };
        return createFn[granularity](date);
    }
    getPeriodicNoteFromPath(path) {
        return __awaiter(this, void 0, void 0, function* () {
            const periods = {
                day: { get: getDailyNote_1, getAll: getAllDailyNotes_1 },
                week: { get: getWeeklyNote_1, getAll: getAllWeeklyNotes_1 },
                month: { get: getMonthlyNote_1, getAll: getAllMonthlyNotes_1 },
                quarter: { get: getQuarterlyNote_1, getAll: getAllQuarterlyNotes_1 },
                year: { get: getYearlyNote_1, getAll: getAllYearlyNotes_1 },
            };
            const result = yield Promise.all(Object.entries(periods).map((entry) => __awaiter(this, void 0, void 0, function* () {
                const [granularity, action] = entry;
                const date = getDateFromPath_1(path, granularity);
                if (date) {
                    const settings = getPeriodicNoteSettings_1(granularity);
                    const resolvedPath = obsidian.normalizePath(pathJoin([settings.folder, (date === null || date === void 0 ? void 0 : date.format(settings.format)) + ".md"]));
                    // console.log(path, date, resolvedPath, settings, granularity);
                    if (path == resolvedPath) {
                        let dnp = action.get(date, action.getAll());
                        if (dnp === null)
                            dnp = yield this.createPeriodicNote(granularity, date);
                        return dnp.path;
                    }
                }
            })));
            return result.find(filePath => filePath);
        });
    }
    applyFileOverrides(workspaceName, workspace) {
        return __awaiter(this, void 0, void 0, function* () {
            let workspaceSettings = this.getWorkspaceSettings(workspaceName);
            if (workspaceSettings === null || workspaceSettings === void 0 ? void 0 : workspaceSettings.fileOverrides) {
                yield Promise.all(Object.entries(workspaceSettings.fileOverrides).map((entry) => __awaiter(this, void 0, void 0, function* () {
                    let [leafId, fileName] = entry;
                    let parsedFileName = this.renderTemplateString(fileName);
                    yield this.getPeriodicNoteFromPath(parsedFileName);
                    const file = this.app.vault.getAbstractFileByPath(obsidian.normalizePath(parsedFileName));
                    // console.log("parsedFileName", parsedFileName, file);
                    if (!file) {
                        fileName = null;
                    }
                    const result = this.setChildId(workspace.main, leafId, file === null || file === void 0 ? void 0 : file.path);
                    // console.log(workspace);
                    if (!result) {
                        // clean up any overrides for panes that no longer exist
                        delete workspaceSettings.fileOverrides[leafId];
                    }
                })));
            }
        });
    }
    getModeSettings(name) {
        if (this.isMode(name))
            return this.getWorkspaceSettings(name);
    }
    updateFoldState(settings) {
        if (settings === null || settings === void 0 ? void 0 : settings.explorerFoldState)
            this.app.saveLocalStorage("file-explorer-unfold", settings.explorerFoldState);
    }
    getDarkModeFromOS() {
        const isDarkMode = window.matchMedia && window.matchMedia("(prefers-color-scheme: dark)").matches;
        return isDarkMode ? "obsidian" : "moonstone";
    }
    updateDarkModeFromOS(settings) {
        settings["theme"] = this.getDarkModeFromOS();
    }
    mergeSidebarLayout(newLayout) {
        const workspace = this.app.workspace;
        const currentLayout = workspace.getLayout();
        newLayout["main"] = currentLayout["main"];
        workspace.changeLayout(newLayout);
    }
    // Template string rendering with math. Credit to Liam Cain https://github.com/liamcain/obsidian-daily-notes-interface
    renderTemplateString(text) {
        const templateOptions = window.app.internalPlugins.getPluginById("templates").instance.options;
        let dateFormat = (templateOptions && templateOptions.dateFormat) || "YYYY-MM-DD";
        let timeFormat = (templateOptions && templateOptions.timeFormat) || "HH:mm";
        const date = window.moment();
        return (text = text
            .replace(/{{\s*(date|time)\s*(([+-]\d+)([yqmwdhs]))?\s*(:.+?)?}}/gi, (_, timeOrDate, calc, timeDelta, unit, momentFormat) => {
            let _format;
            let resolvedDate;
            const now = window.moment();
            if (timeOrDate == "time") {
                _format = timeFormat;
            }
            else {
                _format = dateFormat;
            }
            const currentDate = date.clone().set({
                hour: now.get("hour"),
                minute: now.get("minute"),
                second: now.get("second"),
            });
            if (calc) {
                currentDate.add(parseInt(timeDelta, 10), unit);
            }
            if (momentFormat) {
                resolvedDate = currentDate.format(momentFormat.substring(1).trim());
                // console.log("momentFormat", momentFormat.substring(1).trim(), resolvedDate);
            }
            else {
                resolvedDate = currentDate.format(_format);
            }
            return resolvedDate;
        })
            .replace(/{{\s*yesterday\s*}}/gi, date.clone().subtract(1, "day").format(dateFormat))
            .replace(/{{\s*tomorrow\s*}}/gi, date.clone().add(1, "d").format(dateFormat)));
    }
}

class WorkspacesPlus extends obsidian.Plugin {
    constructor() {
        super(...arguments);
        this.setWorkspaceName = obsidian.debounce(() => {
            var _a, _b, _c;
            if (!this.isNativePluginEnabled) {
                (_a = this.changeWorkspaceButton) === null || _a === void 0 ? void 0 : _a.setText("Error: The Workspaces core plugin is disabled");
            }
            else {
                (_b = this.changeWorkspaceButton) === null || _b === void 0 ? void 0 : _b.setText(this.utils.activeWorkspace);
            }
            if (this.settings.workspaceSettings)
                (_c = this.changeModeButton) === null || _c === void 0 ? void 0 : _c.setText(this.utils.getActiveModeDisplayName());
        }, 100, true);
        this.debouncedSave = obsidian.debounce(
        // avoid overly serializing the workspace during expensive operations like window resize
        (workspaceName) => {
            // avoid errors if the debounced save happens in the middle of a workspace switch
            if (workspaceName === this.utils.activeWorkspace) {
                if (this.debug)
                    console.log("layout invoked save: " + workspaceName);
                this.workspacePlugin.saveWorkspace(workspaceName);
            }
            else {
                if (this.debug)
                    console.log("skipped saving because the workspace has been changed");
            }
        }, 2000, true);
        this.onConfigChange = () => {
            if (!this.settings.workspaceSettings)
                return;
            if (this.workspaceLoading) {
                if (this.debug)
                    console.log("skipped save due to recent workspace switch");
                return;
            }
            const activeModeName = this.utils.activeModeName;
            if (activeModeName) {
                if (this.debug)
                    console.log("config invoked mode update: " + activeModeName);
                this.workspacePlugin.saveWorkspace(activeModeName);
            }
            else {
                if (this.debug)
                    console.log("config invoked global update");
                this.updateGlobalSettings();
            }
        };
        this.onLayoutChange = () => {
            if (!this.workspaceLoading) {
                // TODO: Handle per workspace auto save
                if (this.settings.saveOnChange) {
                    this.debouncedSave(this.utils.activeWorkspace);
                }
            }
        };
        this.onWorkspaceRename = (name, oldName) => {
            this.setWorkspaceName();
            // remove the old command
            this.app.commands.removeCommand(`${this.manifest.id}:${oldName}`);
            const hotkeys = this.app.hotkeyManager.getHotkeys(`${this.manifest.id}:${oldName}`);
            // register the new command
            this.registerWorkspaceHotkeys();
            if (hotkeys) {
                // reassign any hotkeys that were assigned to the old command
                this.app.hotkeyManager.setHotkeys(this.manifest.id + ":" + name, hotkeys);
            }
            // update any cMenu buttons that were associated to the old command
            this.updateCMenuIcon(name, oldName);
            // persist changes to disk
            this.workspacePlugin.saveData();
        };
        this.onWorkspaceDelete = (workspaceName) => {
            this.setWorkspaceName();
            const id = this.manifest.id + ":" + workspaceName;
            this.app.commands.removeCommand(id);
            const hotkeys = this.app.hotkeyManager.getHotkeys(id);
            if (hotkeys) {
                this.app.hotkeyManager.removeHotkeys(this.manifest.id + ":" + workspaceName, hotkeys);
            }
        };
        this.onWorkspaceSave = (workspaceName, customSettings) => __awaiter(this, void 0, void 0, function* () {
            if (!this.isNativePluginEnabled)
                return;
            this.setWorkspaceName();
            this.registerWorkspaceHotkeys();
            if (!customSettings) {
                customSettings = this.utils.getWorkspaceSettings(workspaceName);
            }
            else {
                customSettings = this.utils.setWorkspaceSettings(workspaceName, customSettings);
            }
            if (this.settings.workspaceSettings && this.utils.isMode(workspaceName)) {
                customSettings.app = this.app.vault.config;
            }
            let explorerFoldState = yield this.app.loadLocalStorage("file-explorer-unfold");
            if (explorerFoldState)
                customSettings.explorerFoldState = explorerFoldState;
            this.workspacePlugin.saveData();
        });
        this.onWorkspaceLoad = (name) => {
            this.setWorkspaceName(); // sets status bar text
            this.setWorkspaceAttribute(); // sets HTML data attribute
            this.updatePlatformWorkspace(name);
            const settings = this.utils.getWorkspaceSettings(name);
            if (this.settings.workspaceSettings) {
                const modeName = settings === null || settings === void 0 ? void 0 : settings.mode;
                const mode = modeName && this.utils.getModeSettings(modeName);
                let combinedSettings;
                if (mode) {
                    combinedSettings = this.mergeModeSettings(mode);
                    if (this.debug)
                        console.log("loading mode settings", mode, combinedSettings);
                }
                else {
                    combinedSettings = this.mergeGlobalSettings();
                    if (this.debug)
                        console.log("loading default settings", combinedSettings);
                    settings && (settings["mode"] = null);
                }
                if (this.settings.systemDarkMode)
                    this.utils.updateDarkModeFromOS(combinedSettings);
                this.needsReload(combinedSettings) && this.reloadIfNeeded();
                this.applySettings(combinedSettings);
            }
            if (settings)
                this.utils.updateFoldState(settings);
            this.saveData(this.settings);
        };
        this.reloadIfNeeded = obsidian.debounce(() => {
            function sleep(ms) {
                return new Promise(resolve => setTimeout(resolve, ms));
            }
            // this is currently the only way to tell if CM6 is actually loaded on desktop
            const isLoaded = this.app.commands.editorCommands["editor:toggle-source"] ? true : false;
            const isEnabled = this.app.vault.config.livePreview;
            if (isEnabled != isLoaded) {
                this.app.workspace.saveLayout().then(() => __awaiter(this, void 0, void 0, function* () {
                    while (true) {
                        yield sleep(100);
                        if (this.app.workspace.layoutReady) {
                            return window.location.reload();
                        }
                        else {
                            yield sleep(100);
                        }
                    }
                }));
            }
        }, 500);
    }
    onload() {
        return __awaiter(this, void 0, void 0, function* () {
            this.debug = false;
            // load settings
            yield this.loadSettings();
            this.utils = new Utils(this);
            this.workspacePlugin = this.utils.workspacePlugin;
            this.isNativePluginEnabled = this.utils.isNativePluginEnabled;
            this.installWorkspaceHooks();
            this.registerEvent(this.app.internalPlugins.on("change", plugin => {
                var _a;
                if (((_a = plugin === null || plugin === void 0 ? void 0 : plugin.instance) === null || _a === void 0 ? void 0 : _a.id) == "workspaces") {
                    if (plugin === null || plugin === void 0 ? void 0 : plugin._loaded) {
                        // load
                        this.isNativePluginEnabled = true;
                        // this.setWorkspaceName();
                    }
                    else {
                        // unload
                        this.isNativePluginEnabled = false;
                        // this.setWorkspaceName();
                    }
                }
            }));
            // add the settings tab
            this.addSettingTab(new WorkspacesPlusSettingsTab(this.app, this));
            this.registerEventHandlers();
            this.registerCommands();
            this.app.workspace.onLayoutReady(() => {
                this.setPlatformWorkspace();
                // store current Obsidian settings into local plugin storage
                if (this.settings.workspaceSettings)
                    this.storeGlobalSettings();
                this.backupCoreConfig();
                setTimeout(() => {
                    this.registerWorkspaceHotkeys();
                    this.setWorkspaceAttribute();
                    this.addStatusBarIndicator.apply(this);
                    if (this.settings.workspaceSettings)
                        this.enableModesFeature();
                    if (this.settings.workspaceSwitcherRibbon) {
                        this.toggleWorkspaceRibbonButton();
                        this.toggleNativeWorkspaceRibbon();
                    }
                    if (this.settings.workspaceSettings && this.settings.modeSwitcherRibbon) {
                        this.toggleModeRibbonButton();
                    }
                }, 100);
            });
        });
    }
    backupCoreConfig() {
        this.backupConfigFile("workspaces");
        this.backupConfigFile("app");
        this.backupConfigFile("appearance");
    }
    backupConfigFile(configType) {
        return __awaiter(this, void 0, void 0, function* () {
            const configFileName = this.manifest.dir + `/${configType}.json.bak`;
            const fileExists = yield this.app.vault.exists(configFileName);
            if (!fileExists) {
                const configData = yield this.app.vault.readConfigJson(configType);
                if (configData)
                    return this.app.vault.writeJson(configFileName, configData, true);
            }
        });
    }
    onunload() {
        if (this.settings.workspaceSettings) {
            let combinedSettings = this.mergeGlobalSettings();
            this.applySettings(combinedSettings);
        }
        delete document.body.dataset.workspaceMode;
        delete document.body.dataset.workspaceName;
        if (this.settings.replaceNativeRibbon && this.nativeWorkspaceRibbonItem) {
            this.nativeWorkspaceRibbonItem.show();
        }
    }
    loadSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            this.settings = Object.assign({}, DEFAULT_SETTINGS, yield this.loadData());
        });
    }
    saveSettings() {
        return __awaiter(this, void 0, void 0, function* () {
            yield this.saveData(this.settings);
        });
    }
    registerCommands() {
        this.addCommand({
            id: "open-workspaces-plus",
            name: "Open Workspaces Plus",
            callback: () => new WorkspacesPlusPluginWorkspaceModal(this, this.settings, true).open(),
        });
        this.addCommand({
            id: "save-workspace",
            name: `Save current workspace`,
            callback: () => {
                this.workspacePlugin.saveWorkspace(this.workspacePlugin.activeWorkspace);
                new obsidian.Notice("Successfully saved workspace: " + this.workspacePlugin.activeWorkspace);
            },
        });
    }
    registerEventHandlers() {
        this.registerEvent(this.app.workspace.on("workspace-delete", this.onWorkspaceDelete));
        this.registerEvent(this.app.workspace.on("workspace-rename", this.onWorkspaceRename));
        this.registerEvent(this.app.workspace.on("workspace-save", this.onWorkspaceSave));
        this.registerEvent(this.app.workspace.on("workspace-load", this.onWorkspaceLoad));
        this.registerEvent(this.app.workspace.on("layout-change", this.onLayoutChange));
        this.registerEvent(this.app.workspace.on("resize", this.onLayoutChange));
    }
    get changeWorkspaceButton() {
        var _a;
        return (_a = this.statusBarWorkspace) === null || _a === void 0 ? void 0 : _a.querySelector(".status-bar-item-segment.name");
    }
    get changeModeButton() {
        var _a;
        return (_a = this.statusBarMode) === null || _a === void 0 ? void 0 : _a.querySelector(".status-bar-item-segment.name");
    }
    setPlatformWorkspace() {
        if (!this.isNativePluginEnabled)
            return;
        // note: don't call this too early in the init process or setActiveWorkspace will wipe all workspaces
        const _activeWorkspace = this.app.isMobile
            ? this.settings.activeWorkspaceMobile
            : this.settings.activeWorkspaceDesktop;
        if (_activeWorkspace) {
            this.workspacePlugin.setActiveWorkspace(_activeWorkspace);
        }
    }
    toggleNativeWorkspaceRibbon() {
        var _a, _b;
        if (this.settings.replaceNativeRibbon) {
            if (!this.nativeWorkspaceRibbonItem) {
                this.nativeWorkspaceRibbonItem = document.body.querySelector('[aria-label="Manage workspaces"]');
            }
            (_a = this.nativeWorkspaceRibbonItem) === null || _a === void 0 ? void 0 : _a.hide();
        }
        else {
            (_b = this.nativeWorkspaceRibbonItem) === null || _b === void 0 ? void 0 : _b.show();
        }
    }
    toggleWorkspaceRibbonButton() {
        var _a, _b;
        if (this.settings.workspaceSwitcherRibbon) {
            if (!this.ribbonIconWorkspaces) {
                this.ribbonIconWorkspaces = this.addRibbonIcon("pane-layout", "Manage workspaces", () => __awaiter(this, void 0, void 0, function* () { return new WorkspacesPlusPluginWorkspaceModal(this, this.settings, true).open(); }));
            }
            (_a = this.ribbonIconWorkspaces) === null || _a === void 0 ? void 0 : _a.show();
        }
        else {
            (_b = this.ribbonIconWorkspaces) === null || _b === void 0 ? void 0 : _b.hide();
        }
    }
    toggleModeRibbonButton() {
        var _a, _b;
        if (this.settings.workspaceSettings && this.settings.modeSwitcherRibbon) {
            if (!this.ribbonIconMode) {
                this.ribbonIconMode = this.addRibbonIcon("gear", "Manage modes", () => __awaiter(this, void 0, void 0, function* () { return new WorkspacesPlusPluginModeModal(this, this.settings, true).open(); }));
            }
            (_a = this.ribbonIconMode) === null || _a === void 0 ? void 0 : _a.show();
        }
        else {
            (_b = this.ribbonIconMode) === null || _b === void 0 ? void 0 : _b.hide();
        }
    }
    enableModesFeature() {
        if (this.settings.workspaceSettings) {
            this.storeGlobalSettings();
            this.addStatusBarIndicator("mode");
            this.addCommand({
                id: "open-workspaces-plus-modes",
                name: "Open Workspaces Plus Modes",
                callback: () => new WorkspacesPlusPluginModeModal(this, this.settings, true).open(),
            });
            if (this.debug)
                console.log("toggle load", this.workspacePlugin.activeWorkspace);
            this.onWorkspaceLoad(this.workspacePlugin.activeWorkspace);
            this.registerEvent(this.app.vault.on("config-changed", this.onConfigChange));
        }
    }
    disableModesFeature() {
        var _a;
        this.app.vault.off("config-changed", this.onConfigChange);
        let combinedSettings = this.mergeGlobalSettings();
        this.applySettings(combinedSettings);
        (_a = this.statusBarMode) === null || _a === void 0 ? void 0 : _a.detach();
        this.statusBarMode = null;
        this.app.commands.removeCommand(`${this.manifest.id}:"open-workspaces-plus-modes"`);
    }
    addStatusBarIndicator(modalType = "workspace") {
        let statusBarItem;
        const itemName = modalType == "mode" ? "statusBarMode" : "statusBarWorkspace";
        if (this[itemName])
            return;
        else
            statusBarItem = this[itemName] = this.addStatusBarItem();
        statusBarItem.addClass(`${modalType}-switcher`);
        statusBarItem.setAttribute("aria-label", `Switch ${modalType}`);
        statusBarItem.setAttribute("aria-label-position", "top");
        // create the status bar icon
        const icon = statusBarItem.createSpan("status-bar-item-segment icon");
        modalType == "workspace" ? obsidian.setIcon(icon, "pane-layout") : obsidian.setIcon(icon, "gear"); // inject svg icon
        // create the status bar text
        let modeText = this.utils.getActiveModeDisplayName();
        statusBarItem.createSpan({
            cls: "status-bar-item-segment name",
            text: !this.isNativePluginEnabled
                ? "Error: The Workspaces core plugin is disabled"
                : modalType == "workspace"
                    ? this.utils.activeWorkspace
                    : modeText,
            prepend: false,
        });
        // register click handler
        statusBarItem.addEventListener("click", evt => this.onStatusBarClick(evt, modalType));
    }
    onStatusBarClick(evt, modalType) {
        if (!this.isNativePluginEnabled)
            return;
        // handle the shift click to save current workspace shortcut
        if (evt.shiftKey === true) {
            modalType == "mode" ? this.utils.saveActiveMode() : this.utils.saveActiveWorkspace();
            // why trigger here?
            // this.app.workspace.trigger("layout-change");
            this.registerWorkspaceHotkeys();
            new obsidian.Notice("Successfully saved " + (modalType == "mode" ? "mode" : "workspace"));
        }
        else {
            if (modalType === "workspace")
                new WorkspacesPlusPluginWorkspaceModal(this, this.settings).open();
            if (modalType === "mode")
                new WorkspacesPlusPluginModeModal(this, this.settings).open();
        }
    }
    setWorkspaceAttribute() {
        const workspace = this.utils.activeWorkspace;
        document.body.dataset.workspaceName = workspace;
        if (this.settings.workspaceSettings) {
            const modeName = this.utils.getActiveModeDisplayName();
            if (modeName)
                document.body.dataset.workspaceMode = modeName;
        }
    }
    updateCMenuIcon(name, oldName) {
        const cMenuPlugin = this.app.plugins.plugins["cmenu-plugin"];
        let cMenuItemIdx = cMenuPlugin === null || cMenuPlugin === void 0 ? void 0 : cMenuPlugin.settings.menuCommands.findIndex(cmd => cmd.id === `${this.manifest.id}:${oldName}`);
        if (!cMenuPlugin || cMenuItemIdx === -1)
            return;
        let cMenuItems = cMenuPlugin.settings.menuCommands;
        cMenuItems[cMenuItemIdx].id = `${this.manifest.id}:${name}`;
        cMenuItems[cMenuItemIdx].name = `${this.manifest.name}: Load: ${name}`;
        cMenuPlugin.saveSettings();
        // rebuild the cMenu toolbar
        dispatchEvent(new Event("cMenu-NewCommand"));
    }
    updatePlatformWorkspace(name) {
        if (this.app.isMobile) {
            this.settings.activeWorkspaceMobile = name;
        }
        else {
            this.settings.activeWorkspaceDesktop = name;
        }
    }
    mergeModeSettings(settings) {
        return Object.assign({}, settings["app"]);
    }
    mergeGlobalSettings() {
        return Object.assign({}, this.settings.globalSettings);
    }
    needsReload(settings) {
        return this.settings.reloadLivePreview && settings.livePreview != this.app.vault.config.livePreview;
    }
    applySettings(settings) {
        this.app.disableCssTransition();
        // this emulates what Obsidian does when loading the core settings
        this.app.vault.config = settings;
        this.app.vault.saveConfig();
        // this.app.workspace.updateOptions();
        this.app.setTheme(settings === null || settings === void 0 ? void 0 : settings.theme);
        this.app.customCss.setTheme(settings === null || settings === void 0 ? void 0 : settings.cssTheme);
        // this.app.changeBaseFontSize(settings?.baseFontSize as number);
        this.app.customCss.loadData();
        this.app.customCss.applyCss();
        setTimeout(() => {
            this.app.enableCssTransition();
        }, 1000);
    }
    registerWorkspaceHotkeys() {
        const workspaceNames = Object.keys(this.workspacePlugin.workspaces);
        for (const workspaceName of workspaceNames) {
            this.addCommand({
                id: workspaceName,
                name: `Load: ${workspaceName}`,
                callback: () => {
                    this.workspacePlugin.loadWorkspace(workspaceName);
                },
            });
        }
    }
    setLoadingStatus() {
        this.workspaceLoading = true;
        setTimeout(() => {
            this.workspaceLoading = false;
        }, 2000);
    }
    updateGlobalSettings() {
        this.settings.globalSettings = Object.assign({}, this.settings.globalSettings, this.app.vault.config);
        this.saveData(this.settings);
    }
    storeGlobalSettings() {
        if (Object.keys(this.settings.globalSettings).length === 0) {
            this.settings.globalSettings = Object.assign({}, this.app.vault.config);
            this.saveData(this.settings);
        }
        return this.settings.globalSettings;
    }
    installWorkspaceHooks() {
        // patch the internal workspaces plugin to emit events on save, delete, and load
        const plugin = this;
        this.register(around(this.workspacePlugin, {
            saveWorkspace(old) {
                return function saveWorkspace(workspaceName, ...etc) {
                    // TODO: Does this prevent saving a workspace with no name?
                    if (!workspaceName || !plugin.isNativePluginEnabled)
                        return;
                    let settings;
                    settings = plugin.utils.getWorkspaceSettings(workspaceName);
                    const result = old.call(this, workspaceName, ...etc);
                    if (plugin.debug)
                        console.log("workspace saved: " + workspaceName);
                    this.app.workspace.trigger("workspace-save", workspaceName, settings);
                    return result;
                };
            },
            deleteWorkspace(old) {
                return function deleteWorkspace(workspaceName, ...etc) {
                    if (!workspaceName || !plugin.isNativePluginEnabled)
                        return;
                    const result = old.call(this, workspaceName, ...etc);
                    this.app.workspace.trigger("workspace-delete", workspaceName);
                    return result;
                };
            },
            loadWorkspace(old) {
                return function loadWorkspace(workspaceName, ...etc) {
                    if (!workspaceName || !plugin.isNativePluginEnabled)
                        return;
                    plugin.setLoadingStatus();
                    let result;
                    if (plugin.settings.workspaceSettings && plugin.utils.isMode(workspaceName)) {
                        // if the workspace being loaded is a mode, invoke the mode loader
                        let modeName = workspaceName;
                        workspaceName = plugin.utils.activeWorkspace;
                        result = plugin.utils.loadMode(workspaceName, modeName);
                    }
                    else {
                        // result = old.call(this, workspaceName, ...etc);
                        const workspace = this.workspaces[workspaceName];
                        if (workspace) {
                            // TODO: Ensure this stays in sync with the native Obsidian function
                            this.activeWorkspace = workspaceName;
                            try {
                                plugin.utils.applyFileOverrides(workspaceName, workspace).then(() => {
                                    this.app.workspace.changeLayout(workspace);
                                    this.saveData();
                                });
                            }
                            catch (_a) {
                                console.log("failed to apply overrides");
                                this.app.workspace.changeLayout(workspace);
                                this.saveData();
                            }
                        }
                    }
                    this.app.workspace.trigger("workspace-load", workspaceName);
                    return result;
                };
            },
        }));
    }
}

module.exports = WorkspacesPlus;
